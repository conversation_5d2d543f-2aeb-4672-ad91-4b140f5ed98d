/**
 * Comprehensive Patient Custom Field Synchronization Methods
 *
 * This module provides the main entry points for complete custom field synchronization
 * between AutoPatient (AP) and CliniCore (CC) systems using the enhanced bidirectional
 * synchronization system.
 *
 * Features:
 * - Complete AP to CC custom field synchronization with standard field mapping
 * - Complete CC to AP custom field synchronization with standard/custom field separation
 * - Leverages enhanced bidirectional value extraction, conversion, and validation
 * - <PERSON>les standard field mapping issue (CC custom fields → AP standard fields)
 * - Production-ready with comprehensive error handling and logging
 * - TypeScript compliant with strict type safety
 * - Performance optimized following existing codebase patterns
 *
 * Usage:
 * ```typescript
 * import { updateApToCcCustomFields, updateCcToApCustomFields } from '@/processors/bidirectional/patientSyncMethods';
 *
 * // Sync AP data to CC format
 * const ccPayload = await updateApToCcCustomFields(patientInstance, requestId);
 * await patientReq.update(ccPatientId, { customFields: ccPayload });
 *
 * // Sync CC data to AP format
 * const apPayload = await updateCcToApCustomFields(patientInstance, requestId);
 * await contactReq.update(apContactId, { ...apPayload.standardFields, customFields: apPayload.customFields });
 * ```
 */
import { dbSchema, getDb } from "@database";
import type {
	APGetCustomFieldType,
	GetCCCustomField,
	PostCCPatientCustomfield,
} from "@type";
import { apCustomfield, ccCustomfieldReq, contactReq, patientReq } from "@/apiClient";
import { eq } from "drizzle-orm";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import {
	extractApCustomFieldValues,
	extractCcCustomFieldValues,
	extractApStandardFieldValues,
	createCcCustomFieldPayload,
	createApStandardFieldPayload,
	type CcCustomFieldValue,
} from "./valueHandler";
import { convertCcCustomFieldValueToApCustomField, normalizeGenderValue } from "./valueConverter";
import {
	CC_TO_AP_STANDARD_FIELD_MAPPING,
	getCCFieldVariations,
} from "./fieldMappingConstants";

/**
 * Patient instance type inferred from database schema
 * Automatically stays in sync with database schema definition
 */
export type PatientInstance = typeof dbSchema.patient.$inferSelect;



/**
 * Update AP to CC Custom Fields - Complete Synchronization Flow
 *
 * Implements the complete AP → CC synchronization flow including fresh data fetching,
 * field creation, API updates, and local database updates.
 *
 * @param patientInstance - Local database patient instance with apId and ccId
 * @param requestId - Required request ID for logging and tracing
 * @returns Promise<PatientInstance> - Updated patient instance with fresh data
 *
 * @example
 * ```typescript
 * const updatedPatient = await updateApToCcCustomFields(patient, "req-123");
 * ```
 */
export async function updateApToCcCustomFields(
	patientInstance: PatientInstance,
	requestId: string,
): Promise<PatientInstance> {
	logInfo(requestId, `Starting complete AP to CC custom field synchronization for patient ${patientInstance.id}`);

	try {
		// Step 1: Validate input data
		if (!patientInstance.apId) {
			throw new Error(`Patient ${patientInstance.id} missing AP ID - cannot fetch fresh AP data`);
		}
		if (!patientInstance.ccId) {
			throw new Error(`Patient ${patientInstance.id} missing CC ID - cannot update CC patient`);
		}

		// Step 2: Fetch fresh AP data from API
		logDebug(requestId, `Fetching fresh AP contact data for AP ID: ${patientInstance.apId}`);
		const freshApContactData = await contactReq.get(patientInstance.apId);
		logDebug(requestId, `Fetched fresh AP contact data with ${freshApContactData.customFields?.length || 0} custom fields`);

		// Step 3: Fetch AP field definitions
		logDebug(requestId, "Fetching AP custom field definitions");
		const apCustomFields = await apCustomfield.all();

		if (!apCustomFields || apCustomFields.length === 0) {
			logWarn(
				requestId,
				"No AP custom field definitions available for AP → CC synchronization. " +
				"This means no custom field mappings can be created, but standard field synchronization may still occur. " +
				"Impact: Custom fields from AP will not be synchronized to CC. " +
				"Expected behavior: This is normal if AP has no custom fields configured."
			);
		} else {
			logInfo(requestId, `Retrieved ${apCustomFields.length} AP custom field definitions for mapping`);
			logDebug(requestId, `AP custom field types: ${apCustomFields.map(f => `${f.name}(${f.dataType})`).join(', ')}`);
		}

		// Step 4: Extract and convert AP custom field values
		logDebug(requestId, "Extracting AP custom field values using enhanced system");
		const apCustomFieldValues = await extractApCustomFieldValues(
			freshApContactData,
			apCustomFields,
			requestId,
		);

		logInfo(requestId, `Extracted ${apCustomFieldValues.length} AP custom field values for synchronization`);
		if (apCustomFieldValues.length > 0) {
			logDebug(requestId, `AP custom field values: ${apCustomFieldValues.map(v =>
				`${v.name}="${String(v.value).substring(0, 50)}${String(v.value).length > 50 ? '...' : ''}" (${v.fieldDefinition.dataType})`
			).join(', ')}`);
		}

		// Step 5: Extract AP standard field values for CC custom field mapping
		logDebug(requestId, "Extracting AP standard field values for CC mapping");
		const apStandardFieldValues = extractApStandardFieldValues(freshApContactData, requestId);

		logInfo(requestId, `Extracted ${Object.keys(apStandardFieldValues).length} AP standard field values for CC custom field mapping`);
		if (Object.keys(apStandardFieldValues).length > 0) {
			logDebug(requestId, `AP standard field values: ${Object.entries(apStandardFieldValues).map(([key, value]) =>
				`${key}="${String(value).substring(0, 50)}${String(value).length > 50 ? '...' : ''}"`
			).join(', ')}`);
		}

		// Step 6: Get CC custom field definitions
		logDebug(requestId, "Fetching CC custom field definitions");
		let ccCustomFields = await ccCustomfieldReq.all();

		logInfo(requestId, `Retrieved ${ccCustomFields.length} CC custom field definitions for mapping`);
		if (ccCustomFields.length > 0) {
			logDebug(requestId, `CC custom field types: ${ccCustomFields.map(f => `${f.name}(${f.type})`).join(', ')}`);
		}

		// Step 7: Create CC custom field payload with automatic field creation
		logDebug(requestId, "Creating CC custom field payload with automatic field creation");
		const { payload: ccCustomFieldPayload, createdFields: createdCustomFields } = await createCcCustomFieldPayloadWithCreation(
			apCustomFieldValues,
			ccCustomFields,
			requestId,
		);

		logInfo(requestId, `Created ${ccCustomFieldPayload.length} CC custom field mappings from AP custom fields`);
		if (createdCustomFields.length > 0) {
			logInfo(requestId, `Created ${createdCustomFields.length} new CC custom fields: ${createdCustomFields.map(f => `${f.name}(ID:${f.id})`).join(', ')}`);
		}
		if (ccCustomFieldPayload.length > 0) {
			logDebug(requestId, `CC custom field mappings: ${ccCustomFieldPayload.map(p =>
				`${p.field.name}="${p.values[0]?.value?.substring(0, 50)}${(p.values[0]?.value?.length || 0) > 50 ? '...' : ''}"`
			).join(', ')}`);
		}

		// Step 8: Create CC custom field payload from AP standard fields with automatic creation
		logDebug(requestId, "Creating CC custom field payload from AP standard fields with automatic creation");
		const { payload: standardFieldsAsCustomFields, createdFields: createdStandardFields } = await createCcStandardFieldPayloadWithCreation(
			apStandardFieldValues,
			ccCustomFields,
			requestId,
		);

		logInfo(requestId, `Created ${standardFieldsAsCustomFields.length} CC custom field mappings from AP standard fields`);
		if (createdStandardFields.length > 0) {
			logInfo(requestId, `Created ${createdStandardFields.length} new CC custom fields for AP standard fields: ${createdStandardFields.map(f => `${f.name}(ID:${f.id})`).join(', ')}`);
		}
		if (standardFieldsAsCustomFields.length > 0) {
			logDebug(requestId, `CC standard-to-custom field mappings: ${standardFieldsAsCustomFields.map(p =>
				`${p.field.name}="${p.values[0]?.value?.substring(0, 50)}${(p.values[0]?.value?.length || 0) > 50 ? '...' : ''}"`
			).join(', ')}`);
		}

		// Step 9: Update CC custom fields list with newly created fields
		ccCustomFields = [...ccCustomFields, ...createdCustomFields, ...createdStandardFields];

		// Step 10: Combine both payloads
		const combinedPayload = [...ccCustomFieldPayload, ...standardFieldsAsCustomFields];

		// Enhanced statistics logging
		logApToCcSyncStatistics(
			apCustomFieldValues,
			apStandardFieldValues,
			ccCustomFieldPayload,
			standardFieldsAsCustomFields,
			createdCustomFields,
			createdStandardFields,
			requestId
		);

		if (combinedPayload.length === 0) {
			logInfo(requestId, "No custom fields to sync to CC");
		} else {
			// Step 11: Update CC patient with custom fields
			logInfo(requestId, `Updating CC patient ${patientInstance.ccId} with ${combinedPayload.length} custom fields`);
			await patientReq.update(patientInstance.ccId, {
				customFields: combinedPayload,
			} as any); // Type assertion needed - CC API accepts full objects despite TypeScript definition
			logInfo(requestId, `Successfully updated CC patient with ${combinedPayload.length} custom fields`);
		}

		// Step 12: Update local database with fresh data and timestamps
		logDebug(requestId, "Updating local database with fresh AP data and timestamps");
		const db = getDb();
		const now = new Date();

		const updatedPatientResults = await db
			.update(dbSchema.patient)
			.set({
				apData: freshApContactData,
				apUpdatedAt: now,
				updatedAt: now,
			})
			.where(eq(dbSchema.patient.id, patientInstance.id))
			.returning();

		const updatedPatient = updatedPatientResults[0];
		logInfo(requestId, `Successfully completed AP to CC synchronization for patient ${patientInstance.id}`);

		return updatedPatient;
	} catch (error) {
		logError(requestId, "Failed to complete AP to CC custom field synchronization:", error);
		throw error;
	}
}

/**
 * Update CC to AP Custom Fields - Complete Synchronization Flow
 *
 * Implements the complete CC → AP synchronization flow including fresh data fetching,
 * field creation, API updates, and local database updates.
 *
 * @param patientInstance - Local database patient instance with ccId and apId
 * @param requestId - Required request ID for logging and tracing
 * @returns Promise<PatientInstance> - Updated patient instance with fresh data
 *
 * @example
 * ```typescript
 * const updatedPatient = await updateCcToApCustomFields(patient, "req-123");
 * ```
 */
export async function updateCcToApCustomFields(
	patientInstance: PatientInstance,
	requestId: string,
): Promise<PatientInstance> {
	logInfo(requestId, `Starting complete CC to AP custom field synchronization for patient ${patientInstance.id}`);

	try {
		// Step 1: Validate input data
		if (!patientInstance.ccId) {
			throw new Error(`Patient ${patientInstance.id} missing CC ID - cannot fetch fresh CC data`);
		}
		if (!patientInstance.apId) {
			throw new Error(`Patient ${patientInstance.id} missing AP ID - cannot update AP contact`);
		}

		// Step 2: Fetch fresh CC data from API
		logDebug(requestId, `Fetching fresh CC patient data for CC ID: ${patientInstance.ccId}`);
		const freshCcPatientData = await patientReq.get(patientInstance.ccId);
		logDebug(requestId, `Fetched fresh CC patient data with ${freshCcPatientData.customFields?.length || 0} custom field IDs`);

		// Step 3: Check if patient has custom fields
		if (!freshCcPatientData.customFields || freshCcPatientData.customFields.length === 0) {
			logInfo(requestId, `No custom fields found for CC patient ${freshCcPatientData.id}`);

			// Still update local database with fresh CC data
			const db = getDb();
			const now = new Date();
			const updatedPatientResults = await db
				.update(dbSchema.patient)
				.set({
					ccData: freshCcPatientData,
					ccUpdatedAt: now,
					updatedAt: now,
				})
				.where(eq(dbSchema.patient.id, patientInstance.id))
				.returning();

			return updatedPatientResults[0];
		}

		// Step 4: Fetch CC patient custom field data with values
		logDebug(requestId, `Fetching ${freshCcPatientData.customFields.length} CC patient custom fields`);
		const ccPatientCustomFields = await patientReq.customFields(freshCcPatientData.customFields);

		if (!ccPatientCustomFields || ccPatientCustomFields.length === 0) {
			logWarn(
				requestId,
				"No custom field data returned from CC for CC → AP synchronization. " +
				"This means no custom field values are available to sync to AP. " +
				"Impact: CC custom field values will not be synchronized to AP. " +
				"Expected behavior: This is normal if CC patient has no custom field values set."
			);

			// Still update local database with fresh CC data
			const db = getDb();
			const now = new Date();
			const updatedPatientResults = await db
				.update(dbSchema.patient)
				.set({
					ccData: freshCcPatientData,
					ccUpdatedAt: now,
					updatedAt: now,
				})
				.where(eq(dbSchema.patient.id, patientInstance.id))
				.returning();

			return updatedPatientResults[0];
		}

		logInfo(requestId, `Retrieved ${ccPatientCustomFields.length} CC patient custom field values for synchronization`);
		logDebug(requestId, `CC custom field values: ${ccPatientCustomFields.map(cf =>
			`${cf.field.name}="${cf.values.map(v => v.value).join(', ').substring(0, 50)}${cf.values.map(v => v.value).join(', ').length > 50 ? '...' : ''}" (${cf.field.type})`
		).join(', ')}`);


		// Step 5: Extract CC custom field values using enhanced system
		logDebug(requestId, "Extracting CC custom field values");
		const ccCustomFieldValues = extractCcCustomFieldValues(ccPatientCustomFields, requestId);

		logInfo(requestId, `Extracted ${ccCustomFieldValues.length} CC custom field values for processing`);
		if (ccCustomFieldValues.length > 0) {
			logDebug(requestId, `Extracted CC values: ${ccCustomFieldValues.map(v =>
				`${v.name}="${String(v.value).substring(0, 50)}${String(v.value).length > 50 ? '...' : ''}" (${v.label})`
			).join(', ')}`);
		}

		// Step 6: Get AP custom field definitions
		logDebug(requestId, "Fetching AP custom field definitions");
		let apCustomFields = await apCustomfield.all();

		if (!apCustomFields || apCustomFields.length === 0) {
			logWarn(
				requestId,
				"No AP custom field definitions available for CC → AP synchronization. " +
				"This means CC custom fields cannot be mapped to AP custom fields, but standard field synchronization may still occur. " +
				"Impact: CC custom fields will be mapped to AP standard fields where possible. " +
				"Expected behavior: This is normal if AP has no custom fields configured."
			);
		} else {
			logInfo(requestId, `Retrieved ${apCustomFields.length} AP custom field definitions for mapping`);
			logDebug(requestId, `AP custom field types: ${apCustomFields.map(f => `${f.name}(${f.dataType})`).join(', ')}`);
		}

		// Step 7: Separate standard field mappings from custom field mappings
		const { standardFieldMappings, customFieldMappings } = separateStandardAndCustomFields(
			ccCustomFieldValues,
			requestId,
		);

		logInfo(requestId, `Field separation results: ${Object.keys(standardFieldMappings).length} standard field mappings, ${customFieldMappings.length} custom field mappings`);
		if (Object.keys(standardFieldMappings).length > 0) {
			logDebug(requestId, `CC → AP standard field mappings: ${Object.entries(standardFieldMappings).map(([key, value]) =>
				`${key}="${String(value).substring(0, 50)}${String(value).length > 50 ? '...' : ''}"`
			).join(', ')}`);
		}
		if (customFieldMappings.length > 0) {
			logDebug(requestId, `CC custom fields remaining for AP custom field mapping: ${customFieldMappings.map(cf =>
				`${cf.name}="${String(cf.value).substring(0, 50)}${String(cf.value).length > 50 ? '...' : ''}"`
			).join(', ')}`);
		}

		// Step 8: Create AP custom field payload with automatic field creation
		logDebug(requestId, "Creating AP custom field payload with automatic field creation");
		const { payload: customFieldPayload, createdFields: createdApFields } = await createApCustomFieldPayloadWithCreation(
			customFieldMappings,
			apCustomFields,
			requestId,
		);

		logInfo(requestId, `Created ${customFieldPayload.length} AP custom field mappings from CC custom fields`);
		if (createdApFields.length > 0) {
			logInfo(requestId, `Created ${createdApFields.length} new AP custom fields: ${createdApFields.map(f => `${f.name}(ID:${f.id})`).join(', ')}`);
		}
		if (customFieldPayload.length > 0) {
			logDebug(requestId, `AP custom field mappings: ${customFieldPayload.map(p =>
				`ID:${p.id}="${String(p.field_value).substring(0, 50)}${String(p.field_value).length > 50 ? '...' : ''}"`
			).join(', ')}`);
		}

		// Step 9: Update AP custom fields list with newly created fields
		apCustomFields = [...apCustomFields, ...createdApFields];

		// Step 10: Validate and create AP standard field payload
		logDebug(requestId, "Validating AP standard field mappings");
		const validatedStandardFieldMappings = validateApStandardFieldMappings(standardFieldMappings, requestId);

		logDebug(requestId, "Creating AP standard field payload");
		const standardFieldPayload = createApStandardFieldPayload(validatedStandardFieldMappings, requestId);

		logInfo(requestId, `Created ${Object.keys(standardFieldPayload).length} AP standard field mappings from CC custom fields (${Object.keys(standardFieldMappings).length - Object.keys(validatedStandardFieldMappings).length} fields filtered out)`);
		if (Object.keys(standardFieldPayload).length > 0) {
			logDebug(requestId, `AP standard field mappings: ${Object.entries(standardFieldPayload).map(([key, value]) =>
				`${key}="${String(value).substring(0, 50)}${String(value).length > 50 ? '...' : ''}"`
			).join(', ')}`);
		}

		// Enhanced statistics logging
		logCcToApSyncStatistics(
			ccCustomFieldValues,
			validatedStandardFieldMappings,
			customFieldMappings,
			standardFieldPayload,
			customFieldPayload,
			createdApFields,
			requestId
		);

		// Step 11: Update AP contact with both standard and custom fields
		const hasStandardFields = Object.keys(standardFieldPayload).length > 0;
		const hasCustomFields = customFieldPayload.length > 0;

		let updatedApContact: any = null;

		if (hasStandardFields || hasCustomFields) {
			// Create upsert payload - upsert handles both create and update automatically
			const upsertPayload: any = {
				...standardFieldPayload,
				customFields: customFieldPayload,
			};

			// CRITICAL: Remove tags field to avoid conflicts in upsert operations (as per API docs)
			delete upsertPayload.tags;

			// Enhanced payload logging for debugging
			logInfo(requestId, `Upserting AP contact with ${Object.keys(standardFieldPayload).length} standard fields and ${customFieldPayload.length} custom fields`);
			logDebug(requestId, `AP contact upsert payload standard fields: ${JSON.stringify(standardFieldPayload, null, 2)}`);
			logDebug(requestId, `AP contact upsert payload custom fields: ${JSON.stringify(customFieldPayload, null, 2)}`);
			logDebug(requestId, `Full AP contact upsert payload: ${JSON.stringify(upsertPayload, null, 2)}`);

			try {
				// Use upsert as primary method - it handles create/update automatically based on location settings
				updatedApContact = await contactReq.upsert(upsertPayload);
				logInfo(requestId, `Successfully upserted AP contact with ${Object.keys(standardFieldPayload).length} standard fields and ${customFieldPayload.length} custom fields`);
			} catch (upsertError) {
				logError(requestId, `Upsert failed for AP contact:`, upsertError);

				// If upsert fails, try with custom fields only as fallback
				if (hasCustomFields) {
					logWarn(requestId, `Attempting custom fields only upsert as fallback`);
					try {
						const customFieldsOnlyPayload = {
							customFields: customFieldPayload
							// Note: tags field is already excluded
						};
						updatedApContact = await contactReq.upsert(customFieldsOnlyPayload);
						logInfo(requestId, `Successfully upserted AP contact with custom fields only (${customFieldPayload.length} fields)`);
					} catch (customFieldsError) {
						logError(requestId, `Custom fields only upsert also failed:`, customFieldsError);
						throw customFieldsError;
					}
				} else {
					throw upsertError;
				}
			}
		} else {
			logInfo(requestId, "No fields to sync to AP - using existing AP data");
			// Use existing AP data if no updates were made
			updatedApContact = patientInstance.apData;
		}

		// Step 12: Update local database with fresh data and timestamps
		logDebug(requestId, "Updating local database with fresh CC data and updated AP data");
		const db = getDb();
		const now = new Date();

		const updatedPatientResults = await db
			.update(dbSchema.patient)
			.set({
				ccData: freshCcPatientData,
				apData: updatedApContact,
				ccUpdatedAt: now,
				apUpdatedAt: updatedApContact ? now : patientInstance.apUpdatedAt,
				updatedAt: now,
			})
			.where(eq(dbSchema.patient.id, patientInstance.id))
			.returning();

		const updatedPatient = updatedPatientResults[0];
		logInfo(requestId, `Successfully completed CC to AP synchronization for patient ${patientInstance.id}`);

		return updatedPatient;
	} catch (error) {
		logError(requestId, "Failed to complete CC to AP custom field synchronization:", error);
		throw error;
	}
}



/**
 * Separate CC custom field values into standard field mappings and custom field mappings
 *
 * Uses the CC_TO_AP_STANDARD_FIELD_MAPPING to identify which CC custom fields should
 * be mapped to AP standard fields vs actual custom fields.
 *
 * @param ccCustomFieldValues - Extracted CC custom field values
 * @param requestId - Request ID for logging
 * @returns Separated standard and custom field mappings
 */
function separateStandardAndCustomFields(
	ccCustomFieldValues: CcCustomFieldValue[],
	requestId: string,
): {
	standardFieldMappings: Record<string, string>;
	customFieldMappings: CcCustomFieldValue[];
} {
	const standardFieldMappings: Record<string, string> = {};
	const customFieldMappings: CcCustomFieldValue[] = [];

	for (const ccValue of ccCustomFieldValues) {
		const fieldName = ccValue.name.toLowerCase();
		const fieldLabel = ccValue.label.toLowerCase();

		// Check if this CC custom field should be mapped to an AP standard field
		const apStandardField =
			CC_TO_AP_STANDARD_FIELD_MAPPING[fieldName] ||
			CC_TO_AP_STANDARD_FIELD_MAPPING[fieldLabel];

		if (apStandardField) {
			// This is a standard field mapping
			standardFieldMappings[apStandardField] = ccValue.value;
			logDebug(
				requestId,
				`Mapped CC custom field "${ccValue.label}" to AP standard field "${apStandardField}"`,
			);
		} else {
			// This is a custom field mapping
			customFieldMappings.push(ccValue);
			logDebug(
				requestId,
				`Keeping CC custom field "${ccValue.label}" as custom field`,
			);
		}
	}

	logDebug(
		requestId,
		`Separated ${ccCustomFieldValues.length} CC fields into ${Object.keys(standardFieldMappings).length} standard mappings and ${customFieldMappings.length} custom mappings`,
	);

	return { standardFieldMappings, customFieldMappings };
}

/**
 * Validate AP standard field mappings to ensure they are valid for the AP API
 *
 * Some fields that appear valid might be rejected by the AP API in certain contexts.
 * This function filters out problematic fields and logs warnings.
 *
 * @param standardFieldMappings - Raw standard field mappings
 * @param requestId - Request ID for logging
 * @returns Validated standard field mappings
 */
function validateApStandardFieldMappings(
	standardFieldMappings: Record<string, string>,
	requestId: string,
): Record<string, string> {
	const validatedMappings: Record<string, string> = {};
	// CRITICAL: Exclude tags field for upsert operations as per API documentation
	const problematicFields = new Set(['tags']); // Fields that cause conflicts in upsert operations

	for (const [fieldName, fieldValue] of Object.entries(standardFieldMappings)) {
		// Basic validation - skip empty values
		if (!fieldValue || fieldValue.trim() === '') {
			logDebug(requestId, `Skipping empty standard field: ${fieldName}`);
			continue;
		}

		// Check for problematic fields
		if (problematicFields.has(fieldName)) {
			logWarn(requestId, `Excluding standard field "${fieldName}" = "${fieldValue}" (not allowed in upsert operations per API documentation)`);
			continue;
		}

		// Validate specific field types
		let validatedValue = fieldValue; // Use a mutable variable for validation and normalization

		if (fieldName === 'gender') {
			// Normalize gender value to AP-compatible format (handles international values)
			try {
				const normalizedGender = normalizeGenderValue(fieldValue, requestId);
				validatedValue = normalizedGender;
				logDebug(requestId, `Gender field normalized: "${fieldValue}" → "${normalizedGender}"`);
			} catch (error) {
				logWarn(requestId, `Error normalizing gender value: "${fieldValue}", skipping field: ${error}`);
				continue;
			}
		}

		if (fieldName === 'dnd') {
			// Ensure dnd value is boolean-like
			const normalizedDnd = fieldValue.toLowerCase().trim();
			if (!['true', 'false', '1', '0', 'yes', 'no'].includes(normalizedDnd)) {
				logWarn(requestId, `Invalid dnd value: "${fieldValue}", skipping field`);
				continue;
			}
		}

		// Field passed validation
		validatedMappings[fieldName] = validatedValue;
		logDebug(requestId, `Validated standard field: ${fieldName} = "${fieldValue}"`);
	}

	const originalCount = Object.keys(standardFieldMappings).length;
	const validatedCount = Object.keys(validatedMappings).length;

	if (originalCount !== validatedCount) {
		logWarn(requestId, `Standard field validation: ${validatedCount}/${originalCount} fields passed validation`);
	} else {
		logDebug(requestId, `Standard field validation: all ${validatedCount} fields passed validation`);
	}

	return validatedMappings;
}

/**
 * Create CC custom field payload with automatic field creation
 *
 * Enhanced version of createCcCustomFieldPayload that automatically creates
 * missing CC custom fields when AP fields don't have CC counterparts.
 *
 * @param apCustomFieldValues - Extracted AP custom field values
 * @param ccCustomFields - Existing CC custom field definitions (will be modified)
 * @param requestId - Request ID for logging
 * @returns Object with payload and array of newly created fields
 */
async function createCcCustomFieldPayloadWithCreation(
	apCustomFieldValues: any[],
	ccCustomFields: GetCCCustomField[],
	requestId: string,
): Promise<{ payload: PostCCPatientCustomfield[]; createdFields: GetCCCustomField[] }> {
	const payload: PostCCPatientCustomfield[] = [];
	const createdFields: GetCCCustomField[] = [];

	// First try to create payload with existing fields
	const existingPayload = createCcCustomFieldPayload(apCustomFieldValues, ccCustomFields, requestId);
	payload.push(...existingPayload);

	// Check for AP fields that don't have CC counterparts and create them
	logDebug(requestId, `Checking ${apCustomFieldValues.length} AP custom field values for missing CC counterparts`);
	let matchedCount = 0;
	let createdCount = 0;
	let failedCount = 0;

	for (const apValue of apCustomFieldValues) {
		const existingCcField = ccCustomFields.find(ccField =>
			ccField.name.toLowerCase() === apValue.name.toLowerCase() ||
			ccField.label.toLowerCase() === apValue.name.toLowerCase()
		);

		if (existingCcField) {
			matchedCount++;
			logDebug(requestId, `Found existing CC field match for AP field "${apValue.name}": ${existingCcField.name} (ID: ${existingCcField.id})`);
		} else {
			try {
				logDebug(requestId, `Creating missing CC custom field for AP field: "${apValue.name}" with value: "${String(apValue.value).substring(0, 50)}${String(apValue.value).length > 50 ? '...' : ''}"`);
				const newCcField = await ccCustomfieldReq.create({
					name: apValue.name.toLowerCase().replace(/\s+/g, '-'),
					label: apValue.name,
					type: 'text', // Default to text type
					validation: '{}',
					allowMultipleValues: false,
					isRequired: false,
				});

				createdFields.push(newCcField);
				ccCustomFields.push(newCcField);

				// Add to payload
				payload.push({
					field: newCcField,
					values: [{ value: apValue.value }],
					patient: null,
				});

				createdCount++;
				logInfo(requestId, `Successfully created new CC custom field: "${newCcField.label}" (ID: ${newCcField.id}, Type: ${newCcField.type}) with value: "${String(apValue.value).substring(0, 50)}${String(apValue.value).length > 50 ? '...' : ''}"`);
			} catch (error) {
				failedCount++;
				logError(requestId, `Failed to create CC custom field for "${apValue.name}" with value "${String(apValue.value).substring(0, 50)}${String(apValue.value).length > 50 ? '...' : ''}":`, error);
				// Continue with other fields
			}
		}
	}

	logInfo(requestId, `CC custom field creation summary: ${matchedCount} matched existing, ${createdCount} created new, ${failedCount} failed`);


	return { payload, createdFields };
}

/**
 * Create CC custom field payload from AP standard fields with automatic field creation
 *
 * Enhanced version that automatically creates missing CC custom fields for AP standard fields.
 *
 * @param standardFieldValues - Map of AP standard field names to values
 * @param ccCustomFields - Existing CC custom field definitions (will be modified)
 * @param requestId - Request ID for logging
 * @returns Object with payload and array of newly created fields
 */
async function createCcStandardFieldPayloadWithCreation(
	standardFieldValues: Record<string, string>,
	ccCustomFields: GetCCCustomField[],
	requestId: string,
): Promise<{ payload: PostCCPatientCustomfield[]; createdFields: GetCCCustomField[] }> {
	const payload: PostCCPatientCustomfield[] = [];
	const createdFields: GetCCCustomField[] = [];

	if (Object.keys(standardFieldValues).length === 0) {
		logDebug(requestId, "No AP standard field values to convert to CC custom fields");
		return { payload, createdFields };
	}

	for (const [apFieldName, fieldValue] of Object.entries(standardFieldValues)) {
		const ccFieldVariations = getCCFieldVariations(apFieldName);

		// Try to find matching CC custom field
		let matchedCcField: GetCCCustomField | undefined;

		for (const variation of ccFieldVariations) {
			matchedCcField = ccCustomFields.find(
				(ccField) =>
					ccField.name.toLowerCase() === variation.toLowerCase() ||
					ccField.label.toLowerCase() === variation.toLowerCase(),
			);

			if (matchedCcField) {
				logDebug(
					requestId,
					`Matched AP standard field "${apFieldName}" to existing CC custom field "${matchedCcField.label}" via variation "${variation}"`,
				);
				break;
			}
		}

		if (!matchedCcField) {
			// Create new CC custom field for this AP standard field
			try {
				const primaryVariation = ccFieldVariations[0] || apFieldName;
				logDebug(requestId, `Creating missing CC custom field for AP standard field: ${apFieldName}`);

				const newCcField = await ccCustomfieldReq.create({
					name: primaryVariation.toLowerCase().replace(/\s+/g, '-'),
					label: primaryVariation,
					type: 'text', // Default to text type
					validation: '{}',
					allowMultipleValues: false,
					isRequired: false,
				});

				createdFields.push(newCcField);
				ccCustomFields.push(newCcField);
				matchedCcField = newCcField;

				logInfo(requestId, `Created new CC custom field for standard field: ${newCcField.label} (ID: ${newCcField.id})`);
			} catch (error) {
				logError(requestId, `Failed to create CC custom field for standard field ${apFieldName}:`, error);
				logWarn(
					requestId,
					`No matching CC custom field found for AP standard field "${apFieldName}" (tried variations: ${ccFieldVariations.join(", ")})`,
				);
				continue; // Skip this field and continue with others
			}
		}

		if (matchedCcField) {
			const fieldMapping: PostCCPatientCustomfield = {
				field: matchedCcField,
				values: [{ value: fieldValue }],
				patient: null, // Will be set by CC API
			};

			// Handle allowedValues if present
			if (matchedCcField.allowedValues && matchedCcField.allowedValues.length > 0) {
				const allowedValue = matchedCcField.allowedValues.find(
					(v: any) => v.value?.toLowerCase() === fieldValue.toLowerCase(),
				);
				if (allowedValue) {
					fieldMapping.values = [{ id: allowedValue.id }];
					logDebug(
						requestId,
						`Using allowed value ID ${allowedValue.id} for standard field "${apFieldName}"`,
					);
				}
			}

			payload.push(fieldMapping);
			logDebug(
				requestId,
				`Added standard field mapping: "${apFieldName}" → CC field "${matchedCcField.label}"`,
			);
		}
	}

	logDebug(
		requestId,
		`Created ${payload.length} CC custom field mappings from ${Object.keys(standardFieldValues).length} AP standard fields (${createdFields.length} fields created)`,
	);

	return { payload, createdFields };
}

/**
 * Create AP custom field payload with automatic field creation
 *
 * Enhanced version of createApCustomFieldPayload that automatically creates
 * missing AP custom fields when CC fields don't have AP counterparts.
 *
 * @param ccCustomFieldValues - Extracted CC custom field values
 * @param apCustomFields - Existing AP custom field definitions (will be modified)
 * @param requestId - Request ID for logging
 * @returns Object with payload and array of newly created fields
 */
async function createApCustomFieldPayloadWithCreation(
	ccCustomFieldValues: CcCustomFieldValue[],
	apCustomFields: APGetCustomFieldType[],
	requestId: string,
): Promise<{ payload: { id: string; field_value: string | number }[]; createdFields: APGetCustomFieldType[] }> {
	const payload: { id: string; field_value: string | number }[] = [];
	const createdFields: APGetCustomFieldType[] = [];

	// Process each CC field individually to ensure all are handled
	logDebug(requestId, `Processing ${ccCustomFieldValues.length} CC custom field values for AP mapping`);
	let matchedCount = 0;
	let createdCount = 0;
	let failedCount = 0;

	for (const ccValue of ccCustomFieldValues) {
		// Enhanced field matching logic - check multiple variations
		const existingApField = apCustomFields.find(apField => {
			const apName = apField.name.toLowerCase().trim();
			const ccName = ccValue.name.toLowerCase().trim();
			const ccLabel = ccValue.label.toLowerCase().trim();

			return apName === ccName ||
				   apName === ccLabel ||
				   apName.replace(/[^a-z0-9]/g, '') === ccName.replace(/[^a-z0-9]/g, '') ||
				   apName.replace(/[^a-z0-9]/g, '') === ccLabel.replace(/[^a-z0-9]/g, '');
		});

		if (existingApField) {
			// Field exists - add to payload with value conversion
			matchedCount++;
			logDebug(requestId, `Found existing AP field match for CC field "${ccValue.label}": ${existingApField.name} (ID: ${existingApField.id})`);

			// Convert value if needed
			const convertedValue = convertCcCustomFieldValueToApCustomField(
				ccValue.value,
				ccValue.fieldDefinition,
				existingApField,
				requestId,
			);

			payload.push({
				id: existingApField.id,
				field_value: convertedValue,
			});

			logDebug(requestId, `Added existing field to payload: "${existingApField.name}" = "${String(convertedValue).substring(0, 50)}${String(convertedValue).length > 50 ? '...' : ''}"`);
		} else {
			// Field doesn't exist - create it
			try {
				// Map CC field type to AP field type with proper options handling
				const { dataType, options } = mapCcFieldTypeToApFieldType(ccValue, requestId);

				logDebug(requestId, `Creating missing AP custom field for CC field: "${ccValue.label}" (CC type: ${ccValue.fieldDefinition.type || 'unknown'}) → AP type: ${dataType}${options ? ` with ${options.length} options` : ''}`);

				const createData: any = {
					name: ccValue.label,
					dataType: dataType,
					model: 'contact',
				};

				// Add options for RADIO and MULTIPLE_OPTIONS fields
				if (options && options.length > 0) {
					createData.options = options;
					logDebug(requestId, `Adding options to AP field: [${options.join(', ')}]`);
				}

				const newApField = await apCustomfield.create(createData);

				createdFields.push(newApField);
				apCustomFields.push(newApField);

				// Add to payload with value conversion
				const convertedValue = convertCcCustomFieldValueToApCustomField(
					ccValue.value,
					ccValue.fieldDefinition,
					newApField,
					requestId,
				);

				payload.push({
					id: newApField.id,
					field_value: convertedValue,
				});

				createdCount++;
				logInfo(requestId, `Successfully created new AP custom field: "${newApField.name}" (ID: ${newApField.id}, Type: ${dataType}${options ? `, Options: [${options.join(', ')}]` : ''}) with value: "${String(convertedValue).substring(0, 50)}${String(convertedValue).length > 50 ? '...' : ''}"`);
			} catch (error) {
				failedCount++;
				logError(requestId, `Failed to create AP custom field for "${ccValue.label}" (CC type: ${ccValue.fieldDefinition.type || 'unknown'}):`, error);
				// Continue with other fields
			}
		}
	}

	// Enhanced summary logging
	logInfo(requestId, `AP custom field processing completed: ${matchedCount} matched existing, ${createdCount} created new, ${failedCount} failed`);
	logInfo(requestId, `Final payload contains ${payload.length} AP custom field mappings`);

	return { payload, createdFields };
}

/**
 * Log comprehensive statistics for AP to CC custom field synchronization
 *
 * Provides detailed breakdown of the synchronization process including:
 * - Field mapping details with specific field names and values
 * - Field creation statistics with newly created field information
 * - Processing breakdown between custom fields and standard fields
 * - Success/failure rates and error context
 *
 * @param apCustomFieldValues - Extracted AP custom field values
 * @param apStandardFieldValues - Extracted AP standard field values
 * @param ccCustomFieldPayload - Generated CC custom field payload
 * @param standardFieldsAsCustomFields - Generated CC payload from AP standard fields
 * @param createdCustomFields - Newly created CC custom fields
 * @param createdStandardFields - Newly created CC fields for AP standard fields
 * @param requestId - Request ID for logging
 */
function logApToCcSyncStatistics(
	apCustomFieldValues: any[],
	apStandardFieldValues: Record<string, string>,
	ccCustomFieldPayload: any[],
	standardFieldsAsCustomFields: any[],
	createdCustomFields: GetCCCustomField[],
	createdStandardFields: GetCCCustomField[],
	requestId: string,
): void {
	const totalApCustomFields = apCustomFieldValues.length;
	const totalApStandardFields = Object.keys(apStandardFieldValues).length;
	const totalCcCustomMappings = ccCustomFieldPayload.length;
	const totalCcStandardMappings = standardFieldsAsCustomFields.length;
	const totalCreatedFields = createdCustomFields.length + createdStandardFields.length;
	const totalMappings = totalCcCustomMappings + totalCcStandardMappings;

	// Log summary statistics
	logInfo(requestId, `=== AP → CC Custom Field Sync Statistics ===`);
	logInfo(requestId, `Input Processing:`);
	logInfo(requestId, `  • AP custom fields processed: ${totalApCustomFields}`);
	logInfo(requestId, `  • AP standard fields processed: ${totalApStandardFields}`);
	logInfo(requestId, `  • Total input fields: ${totalApCustomFields + totalApStandardFields}`);

	logInfo(requestId, `Field Mapping Results:`);
	logInfo(requestId, `  • CC custom field mappings created: ${totalCcCustomMappings}`);
	logInfo(requestId, `  • CC standard-to-custom mappings created: ${totalCcStandardMappings}`);
	logInfo(requestId, `  • Total CC field mappings: ${totalMappings}`);

	logInfo(requestId, `Field Creation Results:`);
	logInfo(requestId, `  • New CC custom fields created: ${createdCustomFields.length}`);
	logInfo(requestId, `  • New CC fields for AP standard fields: ${createdStandardFields.length}`);
	logInfo(requestId, `  • Total new CC fields created: ${totalCreatedFields}`);

	// Log successful field mappings
	if (totalCcCustomMappings > 0) {
		logInfo(requestId, `Successfully Mapped AP Custom Fields:`);
		ccCustomFieldPayload.forEach(mapping => {
			const fieldName = mapping.field.name;
			const fieldValue = mapping.values[0]?.value || '';
			const truncatedValue = String(fieldValue).substring(0, 100);
			const displayValue = String(fieldValue).length > 100 ? `${truncatedValue}...` : truncatedValue;
			logInfo(requestId, `  • ${fieldName} = "${displayValue}"`);
		});
	}

	if (totalCcStandardMappings > 0) {
		logInfo(requestId, `Successfully Mapped AP Standard Fields to CC Custom Fields:`);
		standardFieldsAsCustomFields.forEach(mapping => {
			const fieldName = mapping.field.name;
			const fieldValue = mapping.values[0]?.value || '';
			const truncatedValue = String(fieldValue).substring(0, 100);
			const displayValue = String(fieldValue).length > 100 ? `${truncatedValue}...` : truncatedValue;
			logInfo(requestId, `  • ${fieldName} = "${displayValue}"`);
		});
	}

	// Log newly created fields
	if (createdCustomFields.length > 0) {
		logInfo(requestId, `Newly Created CC Custom Fields:`);
		createdCustomFields.forEach(field => {
			logInfo(requestId, `  • ${field.name} (ID: ${field.id}, Type: ${field.type})`);
		});
	}

	if (createdStandardFields.length > 0) {
		logInfo(requestId, `Newly Created CC Fields for AP Standard Fields:`);
		createdStandardFields.forEach(field => {
			logInfo(requestId, `  • ${field.name} (ID: ${field.id}, Type: ${field.type})`);
		});
	}

	// Log processing efficiency
	const mappingEfficiency = totalApCustomFields + totalApStandardFields > 0
		? ((totalMappings / (totalApCustomFields + totalApStandardFields)) * 100).toFixed(1)
		: '0.0';

	logInfo(requestId, `Processing Efficiency:`);
	logInfo(requestId, `  • Mapping success rate: ${mappingEfficiency}% (${totalMappings}/${totalApCustomFields + totalApStandardFields})`);
	logInfo(requestId, `  • Field creation rate: ${totalCreatedFields} new fields created`);
	logInfo(requestId, `=== End AP → CC Sync Statistics ===`);
}

/**
 * Log comprehensive statistics for CC to AP custom field synchronization
 *
 * Provides detailed breakdown of the synchronization process including:
 * - Field separation details between standard and custom field mappings
 * - Field mapping details with specific field names and values
 * - Field creation statistics with newly created field information
 * - Processing breakdown between standard fields and custom fields
 * - Success/failure rates and error context
 *
 * @param ccCustomFieldValues - Extracted CC custom field values
 * @param standardFieldMappings - CC fields mapped to AP standard fields
 * @param customFieldMappings - CC fields mapped to AP custom fields
 * @param standardFieldPayload - Generated AP standard field payload
 * @param customFieldPayload - Generated AP custom field payload
 * @param createdApFields - Newly created AP custom fields
 * @param requestId - Request ID for logging
 */
function logCcToApSyncStatistics(
	ccCustomFieldValues: any[],
	standardFieldMappings: Record<string, string>,
	customFieldMappings: any[],
	standardFieldPayload: Record<string, any>,
	customFieldPayload: any[],
	createdApFields: any[],
	requestId: string,
): void {
	const totalCcCustomFields = ccCustomFieldValues.length;
	const totalStandardMappings = Object.keys(standardFieldMappings).length;
	const totalCustomMappings = customFieldMappings.length;
	const totalApStandardFields = Object.keys(standardFieldPayload).length;
	const totalApCustomFields = customFieldPayload.length;
	const totalCreatedFields = createdApFields.length;
	const totalMappings = totalApStandardFields + totalApCustomFields;

	// Log summary statistics
	logInfo(requestId, `=== CC → AP Custom Field Sync Statistics ===`);
	logInfo(requestId, `Input Processing:`);
	logInfo(requestId, `  • CC custom field values processed: ${totalCcCustomFields}`);
	logInfo(requestId, `  • CC fields mapped to AP standard fields: ${totalStandardMappings}`);
	logInfo(requestId, `  • CC fields mapped to AP custom fields: ${totalCustomMappings}`);

	logInfo(requestId, `Field Mapping Results:`);
	logInfo(requestId, `  • AP standard field mappings created: ${totalApStandardFields}`);
	logInfo(requestId, `  • AP custom field mappings created: ${totalApCustomFields}`);
	logInfo(requestId, `  • Total AP field mappings: ${totalMappings}`);

	logInfo(requestId, `Field Creation Results:`);
	logInfo(requestId, `  • New AP custom fields created: ${totalCreatedFields}`);

	// Log successful standard field mappings
	if (totalApStandardFields > 0) {
		logInfo(requestId, `Successfully Mapped CC Custom Fields to AP Standard Fields:`);
		Object.entries(standardFieldPayload).forEach(([fieldName, fieldValue]) => {
			const truncatedValue = String(fieldValue).substring(0, 100);
			const displayValue = String(fieldValue).length > 100 ? `${truncatedValue}...` : truncatedValue;
			logInfo(requestId, `  • ${fieldName} = "${displayValue}"`);
		});
	}

	// Log successful custom field mappings
	if (totalApCustomFields > 0) {
		logInfo(requestId, `Successfully Mapped CC Custom Fields to AP Custom Fields:`);
		customFieldPayload.forEach(mapping => {
			const fieldValue = mapping.field_value || '';
			const truncatedValue = String(fieldValue).substring(0, 100);
			const displayValue = String(fieldValue).length > 100 ? `${truncatedValue}...` : truncatedValue;
			logInfo(requestId, `  • Field ID ${mapping.id} = "${displayValue}"`);
		});
	}

	// Log newly created fields
	if (createdApFields.length > 0) {
		logInfo(requestId, `Newly Created AP Custom Fields:`);
		createdApFields.forEach(field => {
			logInfo(requestId, `  • ${field.name} (ID: ${field.id}, Type: ${field.dataType})`);
		});
	}

	// Log processing efficiency
	const mappingEfficiency = totalCcCustomFields > 0
		? ((totalMappings / totalCcCustomFields) * 100).toFixed(1)
		: '0.0';

	logInfo(requestId, `Processing Efficiency:`);
	logInfo(requestId, `  • Mapping success rate: ${mappingEfficiency}% (${totalMappings}/${totalCcCustomFields})`);
	logInfo(requestId, `  • Field creation rate: ${totalCreatedFields} new fields created`);
	logInfo(requestId, `  • Standard vs Custom split: ${totalStandardMappings} standard, ${totalCustomMappings} custom`);
	logInfo(requestId, `=== End CC → AP Sync Statistics ===`);
}

/**
 * Map CC field type to AP field type with proper options handling
 *
 * Converts CliniCore field types to AutoPatient field types with appropriate
 * options extraction for select/multiselect fields.
 *
 * @param ccValue - CC custom field value with field definition
 * @param requestId - Request ID for logging
 * @returns Object with AP dataType and options array
 */
function mapCcFieldTypeToApFieldType(
	ccValue: CcCustomFieldValue,
	requestId: string,
): { dataType: string; options?: string[] } {
	const ccField = ccValue.fieldDefinition;
	const ccType = ccField.type.toLowerCase().trim();

	logDebug(requestId, `Mapping CC field type "${ccType}" (allowMultipleValues: ${ccField.allowMultipleValues}) to AP field type`);

	// Handle boolean fields
	if (ccType === 'boolean') {
		logDebug(requestId, `Converting CC boolean field "${ccField.name}" to AP RADIO field with Yes/No options`);
		return {
			dataType: 'RADIO',
			options: ['Yes', 'No']
		};
	}

	// Handle select fields
	if (ccType === 'select') {
		// Extract options from allowedValues
		const options = ccField.allowedValues?.map(av => av.value) || [];

		if (ccField.allowMultipleValues) {
			// CC select with multiple values → AP MULTIPLE_OPTIONS
			logDebug(requestId, `Converting CC select field "${ccField.name}" with multiple values to AP MULTIPLE_OPTIONS (${options.length} options)`);
			return {
				dataType: 'MULTIPLE_OPTIONS',
				options: options.length > 0 ? options : undefined
			};
		} else {
			// CC select single value → AP RADIO
			logDebug(requestId, `Converting CC select field "${ccField.name}" to AP RADIO (${options.length} options)`);
			return {
				dataType: 'RADIO',
				options: options.length > 0 ? options : undefined
			};
		}
	}

	// Handle select-or-custom fields
	if (ccType === 'select-or-custom') {
		// Extract options from allowedValues
		const options = ccField.allowedValues?.map(av => av.value) || [];

		if (options.length > 0) {
			// Has predefined options, create RADIO field
			logDebug(requestId, `Converting CC select-or-custom field "${ccField.name}" to AP RADIO with ${options.length} predefined options`);
			return {
				dataType: 'RADIO',
				options: options
			};
		} else {
			// No predefined options, fallback to TEXT for maximum compatibility
			logWarn(requestId, `CC select-or-custom field "${ccField.name}" has no allowedValues, falling back to TEXT type`);
			return {
				dataType: 'TEXT'
			};
		}
	}

	// Handle multiselect fields (explicit type)
	if (ccType === 'multiselect') {
		const options = ccField.allowedValues?.map(av => av.value) || [];
		logDebug(requestId, `Converting CC multiselect field "${ccField.name}" to AP MULTIPLE_OPTIONS (${options.length} options)`);
		return {
			dataType: 'MULTIPLE_OPTIONS',
			options: options.length > 0 ? options : undefined
		};
	}

	// Handle text fields
	if (ccType === 'text' || ccType === 'textarea') {
		logDebug(requestId, `Converting CC ${ccType} field "${ccField.name}" to AP TEXT`);
		return {
			dataType: 'TEXT'
		};
	}

	// Handle phone fields (including telephone/phone mismatch compatibility)
	if (ccType === 'phone' || ccType === 'telephone') {
		logDebug(requestId, `Converting CC ${ccType} field "${ccField.name}" to AP PHONE`);
		return {
			dataType: 'PHONE'
		};
	}

	// Handle email fields
	if (ccType === 'email') {
		logDebug(requestId, `Converting CC email field "${ccField.name}" to AP EMAIL`);
		return {
			dataType: 'EMAIL'
		};
	}

	// Handle number fields
	if (ccType === 'number' || ccType === 'decimal' || ccType === 'currency') {
		logDebug(requestId, `Converting CC ${ccType} field "${ccField.name}" to AP NUMBER`);
		return {
			dataType: 'NUMBER'
		};
	}

	// Handle date fields
	if (ccType === 'date' || ccType === 'datetime') {
		logDebug(requestId, `Converting CC ${ccType} field "${ccField.name}" to AP DATE`);
		return {
			dataType: 'DATE'
		};
	}

	// Handle file upload fields
	if (ccType === 'file' || ccType === 'upload' || ccType === 'attachment') {
		logDebug(requestId, `Converting CC ${ccType} field "${ccField.name}" to AP FILE_UPLOAD`);
		return {
			dataType: 'FILE_UPLOAD'
		};
	}

	// Default fallback to TEXT for unknown types
	logWarn(requestId, `Field type incompatibility detected for "${ccField.name}": ${ccType} → TEXT. Falling back to TEXT type for maximum compatibility.`);
	return {
		dataType: 'TEXT'
	};
}
