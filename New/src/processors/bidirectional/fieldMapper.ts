/**
 * Bidirectional Field Mapping Module
 *
 * Provides comprehensive field definition mapping between AutoPatient (AP) and CliniCore (CC) systems.
 * Handles conversion of field definitions, options, validation rules, and metadata while preserving
 * data integrity and ensuring reversible transformations.
 *
 * Features:
 * - AP to CC custom field definition mapping
 * - CC to AP custom field definition mapping
 * - Option value conversion and mapping
 * - Validation rule preservation
 * - Field metadata handling
 * - Production-ready error handling and logging
 */

import type {
	APGetCustomFieldType,
	APPostCustomfieldType,
	GetCCCustomField,
	PostCCCustomField,
} from "@type";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import {
	getExpectedApType,
	getExpectedCcType,
	isApBooleanRadioField,
	isCcBooleanField,
	validateFieldDefinition,
} from "./typeCompatibility";

/**
 * Map AutoPatient custom field definitions to CliniCore format
 *
 * Converts AP field definitions to CC format for synchronization.
 * Handles field type mapping, option conversion, and validation rules.
 *
 * @param apFields - Array of AP custom field definitions
 * @param requestId - Request ID for logging
 * @returns Array of CC custom field definitions ready for creation
 */
export function mapApToCcCustomFields(
	apFields: APGetCustomFieldType[],
	requestId: string,
): PostCCCustomField[] {
	const mappedFields: PostCCCustomField[] = [];
	let successCount = 0;
	let errorCount = 0;

	logInfo(requestId, `Starting AP to CC field mapping for ${apFields.length} fields`);

	for (const apField of apFields) {
		try {
			// Validate field definition
			if (!validateFieldDefinition(apField.name, apField.dataType, requestId)) {
				logWarn(requestId, `Skipping invalid AP field: "${apField.name}"`);
				errorCount++;
				continue;
			}

			// Map field type
			const ccFieldType = getExpectedCcType(apField, requestId);

			// Create base CC field definition
			const ccField: PostCCCustomField = {
				name: normalizeFieldName(apField.name),
				label: apField.name, // Use original name as label for user display
				type: ccFieldType,
				validation: "{}",
				color: null,
				allowMultipleValues: apField.dataType === "MULTIPLE_OPTIONS",
				useCustomSort: null,
				isRequired: false, // AP doesn't have required field info in the provided types
			};

			// Handle field-specific mappings
			if (isApBooleanRadioField(apField)) {
				// Boolean RADIO fields don't need allowedValues in CC
				ccField.allowedValues = [];
				ccField.defaultValues = [];
			} else if (apField.dataType === "RADIO" || apField.dataType === "MULTIPLE_OPTIONS") {
				// Map options for select fields
				const options = extractApFieldOptions(apField);
				ccField.allowedValues = options.map(option => ({ value: option }));
				ccField.defaultValues = [];
			} else {
				// Other field types don't have options
				ccField.allowedValues = [];
				ccField.defaultValues = [];
			}

			mappedFields.push(ccField);
			successCount++;

			logDebug(
				requestId,
				`Mapped AP field "${apField.name}" (${apField.dataType}) → CC "${ccField.name}" (${ccField.type})`,
			);
		} catch (error) {
			logError(
				requestId,
				`Error mapping AP field "${apField.name}":`,
				error,
			);
			errorCount++;
		}
	}

	logInfo(
		requestId,
		`AP to CC field mapping completed: ${successCount} successful, ${errorCount} errors`,
	);

	return mappedFields;
}

/**
 * Map CliniCore custom field definitions to AutoPatient format
 *
 * Converts CC field definitions to AP format for synchronization.
 * Handles field type mapping, option conversion, and validation rules.
 *
 * @param ccFields - Array of CC custom field definitions
 * @param requestId - Request ID for logging
 * @returns Array of AP custom field definitions ready for creation
 */
export function mapCcToApCustomFields(
	ccFields: GetCCCustomField[],
	requestId: string,
): APPostCustomfieldType[] {
	const mappedFields: APPostCustomfieldType[] = [];
	let successCount = 0;
	let errorCount = 0;

	logInfo(requestId, `Starting CC to AP field mapping for ${ccFields.length} fields`);

	for (const ccField of ccFields) {
		try {
			// Validate field definition
			if (!validateFieldDefinition(ccField.name, ccField.type, requestId)) {
				logWarn(requestId, `Skipping invalid CC field: "${ccField.name}"`);
				errorCount++;
				continue;
			}

			// Map field type
			const apFieldType = getExpectedApType(ccField, requestId);

			// Generate field key
			const fieldKey = generateApFieldKey(ccField.name);

			// Create base AP field definition
			const apField: APPostCustomfieldType = {
				name: ccField.label || ccField.name, // Use label if available, fallback to name
				dataType: apFieldType,
				model: "contact", // Default to contact model
				fieldKey: fieldKey,
				placeholder: "",
			};

			// Handle field-specific mappings
			if (isCcBooleanField(ccField)) {
				// Boolean fields become RADIO with Yes/No options
				apField.options = ["Yes", "No"];
			} else if (ccField.type.toLowerCase() === "select" || ccField.type.toLowerCase() === "multiselect") {
				// Map options for select fields
				const options = extractCcFieldOptions(ccField);
				apField.options = options;
			}

			// Handle multiple values
			if (ccField.allowMultipleValues && apField.dataType === "RADIO") {
				apField.dataType = "MULTIPLE_OPTIONS";
			}

			mappedFields.push(apField);
			successCount++;

			logDebug(
				requestId,
				`Mapped CC field "${ccField.name}" (${ccField.type}) → AP "${apField.name}" (${apField.dataType})`,
			);
		} catch (error) {
			logError(
				requestId,
				`Error mapping CC field "${ccField.name}":`,
				error,
			);
			errorCount++;
		}
	}

	logInfo(
		requestId,
		`CC to AP field mapping completed: ${successCount} successful, ${errorCount} errors`,
	);

	return mappedFields;
}

/**
 * Extract options from AP custom field definition
 *
 * @param apField - AP custom field definition
 * @returns Array of option values
 */
function extractApFieldOptions(apField: APGetCustomFieldType): string[] {
	// Check picklistOptions first (most common)
	if ((apField as any).picklistOptions && Array.isArray((apField as any).picklistOptions)) {
		return (apField as any).picklistOptions;
	}

	// Check textBoxListOptions
	if (apField.textBoxListOptions && Array.isArray(apField.textBoxListOptions)) {
		return apField.textBoxListOptions.map(option => option.label);
	}

	// Check options array (from API response)
	if ((apField as any).options && Array.isArray((apField as any).options)) {
		const options = (apField as any).options;
		// Handle both string array and object array formats
		return options.map((option: any) => 
			typeof option === "string" ? option : (option.label || option.key || String(option))
		);
	}

	return [];
}

/**
 * Extract options from CC custom field definition
 *
 * @param ccField - CC custom field definition
 * @returns Array of option values
 */
function extractCcFieldOptions(ccField: GetCCCustomField): string[] {
	if (!ccField.allowedValues || !Array.isArray(ccField.allowedValues)) {
		return [];
	}

	return ccField.allowedValues.map(allowedValue => allowedValue.value);
}

/**
 * Normalize field name for CC system
 *
 * @param fieldName - Original field name
 * @returns Normalized field name suitable for CC
 */
function normalizeFieldName(fieldName: string): string {
	return fieldName
		.toLowerCase()
		.trim()
		.replace(/[^a-z0-9]/g, "-") // Replace non-alphanumeric with hyphens
		.replace(/-+/g, "-") // Replace multiple hyphens with single hyphen
		.replace(/^-|-$/g, ""); // Remove leading/trailing hyphens
}

/**
 * Generate AP field key from field name
 *
 * @param fieldName - Field name
 * @returns Generated field key in format "contact.field_name"
 */
function generateApFieldKey(fieldName: string): string {
	const normalizedName = fieldName
		.toLowerCase()
		.trim()
		.replace(/[^a-z0-9]/g, "_") // Replace non-alphanumeric with underscores
		.replace(/_+/g, "_") // Replace multiple underscores with single underscore
		.replace(/^_|_$/g, ""); // Remove leading/trailing underscores

	return `contact.${normalizedName}`;
}

/**
 * Map field validation rules from AP to CC format
 *
 * @param apField - AP custom field definition
 * @returns CC validation rules as JSON string
 */
function mapApToCcValidation(apField: APGetCustomFieldType): string {
	const validation: Record<string, any> = {};

	// Add validation based on field type
	switch (apField.dataType) {
		case "EMAIL":
			validation.email = true;
			break;
		case "PHONE":
			validation.phone = true;
			break;
		case "NUMBER":
			validation.numeric = true;
			break;
		case "DATE":
			validation.date = true;
			break;
	}

	// Add file validation for file upload fields
	if (apField.dataType === "FILE_UPLOAD") {
		if (apField.acceptedFormat && apField.acceptedFormat.length > 0) {
			validation.acceptedFormats = apField.acceptedFormat;
		}
		if (apField.maxNumberOfFiles) {
			validation.maxFiles = apField.maxNumberOfFiles;
		}
	}

	return JSON.stringify(validation);
}

/**
 * Map field validation rules from CC to AP format
 *
 * @param ccField - CC custom field definition
 * @returns AP field properties object
 */
function mapCcToApValidation(ccField: GetCCCustomField): Partial<APPostCustomfieldType> {
	const properties: Partial<APPostCustomfieldType> = {};

	try {
		const validation = JSON.parse(ccField.validation || "{}");

		// Map validation rules to AP field properties
		if (validation.maxFiles) {
			properties.maxNumberOfFiles = validation.maxFiles;
		}
		if (validation.acceptedFormats && Array.isArray(validation.acceptedFormats)) {
			properties.acceptedFormat = validation.acceptedFormats;
		}
	} catch (error) {
		// Invalid JSON validation, ignore
	}

	return properties;
}
