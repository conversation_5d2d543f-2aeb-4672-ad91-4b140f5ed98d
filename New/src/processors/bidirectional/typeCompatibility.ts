/**
 * Bidirectional Type Compatibility Module
 *
 * Provides comprehensive field type compatibility checking and validation for
 * bidirectional synchronization between AutoPatient (AP) and CliniCore (CC) systems.
 *
 * Features:
 * - Enhanced field type compatibility matrix
 * - Graceful handling of mismatched but compatible types
 * - Validation functions for field definitions
 * - Support for PHONE/telephone, TEXT/textarea, boolean/radio conversions
 * - Production-ready error handling and logging
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { logDebug, logWarn } from "@/utils/logger";

/**
 * AP to CC field type mapping for bidirectional conversion
 */
export const AP_TO_CC_TYPE_MAPPING: Record<string, string> = {
	// Text fields
	TEXT: "text",
	TEXTAREA: "textarea",
	
	// Selection fields
	RADIO: "select", // Will be overridden to "boolean" for Yes/No fields
	MULTIPLE_OPTIONS: "multiselect",
	
	// Input fields
	PHONE: "phone",
	EMAIL: "email",
	NUMBER: "number",
	
	// Date and time
	DATE: "date",
	TIME: "time",
	DATETIME: "datetime",
	
	// File fields
	FILE_UPLOAD: "file",
	
	// Special fields
	SIGNATURE: "signature",
	
	// Default fallback
	default: "text",
};

/**
 * CC to AP field type mapping for bidirectional conversion
 */
export const CC_TO_AP_TYPE_MAPPING: Record<string, string> = {
	// Text fields
	text: "TEXT",
	textarea: "TEXTAREA",
	string: "TEXT",
	varchar: "TEXT",
	
	// Selection fields
	boolean: "RADIO", // Will have Yes/No options
	select: "RADIO",
	"select-or-custom": "RADIO", // Handle select-or-custom fields as RADIO with fallback to TEXT
	multiselect: "MULTIPLE_OPTIONS",
	checkbox: "MULTIPLE_OPTIONS",
	radio: "RADIO",
	
	// Input fields
	phone: "PHONE",
	telephone: "PHONE", // Handle telephone/phone mismatch
	email: "EMAIL",
	number: "NUMBER",
	decimal: "NUMBER",
	float: "NUMBER",
	integer: "NUMBER",
	
	// Date and time
	date: "DATE",
	time: "TIME",
	datetime: "DATETIME",
	timestamp: "DATETIME",
	
	// File fields
	file: "FILE_UPLOAD",
	upload: "FILE_UPLOAD",
	attachment: "FILE_UPLOAD",
	
	// Special fields
	signature: "SIGNATURE",
	
	// Default fallback
	default: "TEXT",
};

/**
 * Enhanced field type compatibility matrix for mismatched but compatible types
 * Each key represents a field type, and the array contains compatible types
 *
 * ENHANCED: Comprehensive compatibility matrix with graceful handling of common mismatches
 */
export const TYPE_COMPATIBILITY_MATRIX: Record<string, string[]> = {
	// Text field compatibility (enhanced with more variations)
	text: ["textarea", "string", "varchar", "email", "phone", "telephone", "url", "website"],
	textarea: ["text", "string", "varchar", "longtext", "memo"],
	string: ["text", "textarea", "varchar", "char"],
	varchar: ["text", "textarea", "string", "char"],
	char: ["text", "string", "varchar"],
	longtext: ["textarea", "text", "memo"],
	memo: ["textarea", "longtext", "text"],

	// Phone field compatibility (enhanced with international variations)
	phone: ["telephone", "text", "string", "varchar", "mobile", "cell", "cellular"],
	telephone: ["phone", "text", "string", "varchar", "mobile", "cell"],
	mobile: ["phone", "telephone", "cell", "cellular", "text"],
	cell: ["phone", "telephone", "mobile", "cellular", "text"],
	cellular: ["phone", "telephone", "mobile", "cell", "text"],

	// Boolean field compatibility (enhanced with more variations)
	boolean: ["select", "radio", "text", "checkbox", "yesno"],
	select: ["boolean", "radio", "text", "textarea", "dropdown"],
	radio: ["boolean", "select", "text", "checkbox"],
	checkbox: ["boolean", "multiselect", "text", "textarea", "radio"],
	yesno: ["boolean", "radio", "select", "text"],
	dropdown: ["select", "radio", "text"],

	// Number field compatibility (enhanced with more numeric types)
	number: ["decimal", "float", "integer", "text", "string", "numeric", "double"],
	decimal: ["number", "float", "text", "string", "numeric", "double"],
	float: ["number", "decimal", "text", "string", "double", "real"],
	integer: ["number", "text", "string", "int", "bigint"],
	numeric: ["number", "decimal", "float", "text", "string"],
	double: ["number", "decimal", "float", "text", "string"],
	real: ["float", "number", "decimal", "text", "string"],
	int: ["integer", "number", "text", "string"],
	bigint: ["integer", "number", "text", "string"],

	// Multi-value field compatibility (enhanced)
	multiselect: ["checkbox", "text", "textarea", "multiple_options"],
	multiple_options: ["multiselect", "checkbox", "text", "textarea"],

	// Email field compatibility (enhanced)
	email: ["text", "string", "varchar", "mail", "e-mail"],
	mail: ["email", "text", "string"],
	"e-mail": ["email", "text", "string"],

	// Date and time field compatibility (enhanced)
	date: ["datetime", "timestamp", "text", "string", "dateonly"],
	datetime: ["date", "timestamp", "text", "string", "datetimelocal"],
	timestamp: ["date", "datetime", "text", "string"],
	time: ["text", "string", "timeonly"],
	dateonly: ["date", "text", "string"],
	timeonly: ["time", "text", "string"],
	datetimelocal: ["datetime", "text", "string"],

	// File field compatibility (enhanced)
	file: ["upload", "attachment", "text", "file_upload"],
	upload: ["file", "attachment", "text", "file_upload"],
	attachment: ["file", "upload", "text"],
	file_upload: ["file", "upload", "attachment", "text"],

	// URL/Website field compatibility
	url: ["website", "text", "string", "varchar"],
	website: ["url", "text", "string", "varchar"],

	// Signature field compatibility
	signature: ["text", "file", "upload", "attachment"],

	// Special AP field types
	PHONE: ["phone", "telephone", "text", "string"],
	EMAIL: ["email", "text", "string"],
	TEXT: ["text", "textarea", "string", "varchar"],
	TEXTAREA: ["textarea", "text", "string", "longtext"],
	RADIO: ["radio", "select", "boolean", "text"],
	MULTIPLE_OPTIONS: ["multiselect", "checkbox", "text", "textarea"],
	NUMBER: ["number", "decimal", "float", "integer", "text"],
	DATE: ["date", "datetime", "text", "string"],
	TIME: ["time", "text", "string"],
	DATETIME: ["datetime", "date", "timestamp", "text"],
	FILE_UPLOAD: ["file", "upload", "attachment", "text"],
	SIGNATURE: ["signature", "text", "file"],
};

/**
 * Check if two field types are compatible for conversion
 *
 * @param sourceType - Source field type (normalized to lowercase)
 * @param targetType - Target field type (normalized to lowercase)
 * @returns true if types are compatible for conversion
 */
export function areTypesCompatible(sourceType: string, targetType: string): boolean {
	const normalizedSource = sourceType.toLowerCase().trim();
	const normalizedTarget = targetType.toLowerCase().trim();
	
	// Direct type match
	if (normalizedSource === normalizedTarget) {
		return true;
	}
	
	// Check compatibility matrix
	const compatibleTypes = TYPE_COMPATIBILITY_MATRIX[normalizedSource] || [];
	return compatibleTypes.includes(normalizedTarget);
}

/**
 * Enhanced validation for AP to CC field type compatibility
 *
 * Provides comprehensive compatibility checking with graceful handling of mismatches.
 * Includes special handling for PHONE/telephone and TEXT/textarea mismatches.
 *
 * @param apField - AP custom field definition
 * @param ccField - CC custom field definition
 * @param requestId - Request ID for logging
 * @returns Compatibility result with detailed information
 */
export function validateApToCcCompatibility(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): boolean {
	const compatibilityResult = checkFieldTypeCompatibility(
		apField.dataType,
		ccField.type,
		apField.name,
		"AP→CC",
		requestId
	);

	// Log the result
	if (compatibilityResult.isCompatible) {
		logDebug(
			requestId,
			`Field types compatible: AP ${apField.dataType} → CC ${ccField.type} for field "${apField.name}" (${compatibilityResult.compatibilityLevel})`,
		);
	} else if (compatibilityResult.isGracefullyHandled) {
		logWarn(
			requestId,
			`Type compatibility warning: AP ${apField.dataType} → CC ${ccField.type} for field "${apField.name}". ${compatibilityResult.message}`,
		);
	}

	// Return true for both compatible and gracefully handled cases
	return compatibilityResult.isCompatible || compatibilityResult.isGracefullyHandled;
}

/**
 * Enhanced validation for CC to AP field type compatibility
 *
 * Provides comprehensive compatibility checking with graceful handling of mismatches.
 * Includes special handling for PHONE/telephone and TEXT/textarea mismatches.
 *
 * @param ccField - CC custom field definition
 * @param apField - AP custom field definition
 * @param requestId - Request ID for logging
 * @returns Compatibility result with detailed information
 */
export function validateCcToApCompatibility(
	ccField: GetCCCustomField,
	apField: APGetCustomFieldType,
	requestId: string,
): boolean {
	const compatibilityResult = checkFieldTypeCompatibility(
		ccField.type,
		apField.dataType,
		ccField.name,
		"CC→AP",
		requestId
	);

	// Log the result
	if (compatibilityResult.isCompatible) {
		logDebug(
			requestId,
			`Field types compatible: CC ${ccField.type} → AP ${apField.dataType} for field "${ccField.name}" (${compatibilityResult.compatibilityLevel})`,
		);
	} else if (compatibilityResult.isGracefullyHandled) {
		logWarn(
			requestId,
			`Type compatibility warning: CC ${ccField.type} → AP ${apField.dataType} for field "${ccField.name}". ${compatibilityResult.message}`,
		);
	}

	// Return true for both compatible and gracefully handled cases
	return compatibilityResult.isCompatible || compatibilityResult.isGracefullyHandled;
}

/**
 * Get expected CC field type for an AP field with enhanced fallback logic
 *
 * @param apField - AP custom field definition
 * @param requestId - Optional request ID for logging
 * @returns Expected CC field type with fallback to text for unknown types
 */
export function getExpectedCcType(apField: APGetCustomFieldType, requestId?: string): string {
	// Special handling for boolean RADIO fields
	if (isApBooleanRadioField(apField)) {
		if (requestId) {
			logDebug(requestId, `AP boolean RADIO field mapped to CC boolean: "${apField.name}"`);
		}
		return "boolean";
	}

	// Try standard mapping first
	const mappedType = AP_TO_CC_TYPE_MAPPING[apField.dataType];
	if (mappedType) {
		return mappedType;
	}

	// Fallback to text for unknown types
	if (requestId) {
		logWarn(requestId, `Unknown AP field type "${apField.dataType}" for field "${apField.name}", falling back to text type`);
	}
	return "text";
}

/**
 * Get expected AP field type for a CC field with enhanced fallback logic
 *
 * @param ccField - CC custom field definition
 * @param requestId - Optional request ID for logging
 * @returns Expected AP field type with fallback to TEXT for unknown types
 */
export function getExpectedApType(ccField: GetCCCustomField, requestId?: string): string {
	const ccType = ccField.type.toLowerCase().trim();

	// Special handling for select fields with multiple values
	if (ccType === "select" && ccField.allowMultipleValues === true) {
		if (requestId) {
			logDebug(requestId, `CC select field with multiple values mapped to AP MULTIPLE_OPTIONS: "${ccField.name}"`);
		}
		return "MULTIPLE_OPTIONS";
	}

	// Special handling for select-or-custom fields
	if (ccType === "select-or-custom") {
		// Check if field has allowedValues to determine if it can be RADIO or should fallback to TEXT
		if (ccField.allowedValues && ccField.allowedValues.length > 0) {
			if (requestId) {
				logDebug(requestId, `CC select-or-custom field with ${ccField.allowedValues.length} options mapped to AP RADIO: "${ccField.name}"`);
			}
			return "RADIO";
		} else {
			// No predefined options, fallback to TEXT for maximum compatibility
			if (requestId) {
				logWarn(requestId, `CC select-or-custom field "${ccField.name}" has no allowedValues, falling back to TEXT type`);
			}
			return "TEXT";
		}
	}

	// Try standard mapping first
	const mappedType = CC_TO_AP_TYPE_MAPPING[ccType];
	if (mappedType) {
		return mappedType;
	}

	// Fallback to TEXT for unknown types
	if (requestId) {
		logWarn(requestId, `Unknown CC field type "${ccType}" for field "${ccField.name}", falling back to TEXT type`);
	}
	return "TEXT";
}

/**
 * Check if an AP field is a boolean RADIO field (Yes/No options)
 *
 * @param apField - AP custom field definition
 * @returns true if field is a boolean RADIO field
 */
export function isApBooleanRadioField(apField: APGetCustomFieldType): boolean {
	if (apField.dataType !== "RADIO") {
		return false;
	}
	
	// Check if field has Yes/No options (from picklistOptions or textBoxListOptions)
	const options = (apField as any).picklistOptions || 
		(apField.textBoxListOptions?.map(opt => opt.label)) || [];
	
	if (options.length !== 2) {
		return false;
	}
	
	const normalizedOptions = options.map((opt: string) => opt.toLowerCase().trim());
	return normalizedOptions.includes("yes") && normalizedOptions.includes("no");
}

/**
 * Check if a CC field is a boolean field
 *
 * @param ccField - CC custom field definition
 * @returns true if field is a boolean field
 */
export function isCcBooleanField(ccField: GetCCCustomField): boolean {
	return ccField.type.toLowerCase().trim() === "boolean";
}

/**
 * Validate field definition for creation
 *
 * @param fieldName - Field name to validate
 * @param fieldType - Field type to validate
 * @param requestId - Request ID for logging
 * @returns true if field definition is valid
 */
export function validateFieldDefinition(
	fieldName: string,
	fieldType: string,
	requestId: string,
): boolean {
	// Validate field name
	if (!fieldName || fieldName.trim().length === 0) {
		logWarn(requestId, "Field name cannot be empty");
		return false;
	}
	
	// Validate field type
	if (!fieldType || fieldType.trim().length === 0) {
		logWarn(requestId, `Field type cannot be empty for field "${fieldName}"`);
		return false;
	}
	
	// Check if field name contains invalid characters
	const invalidChars = /[<>:"\/\\|?*]/;
	if (invalidChars.test(fieldName)) {
		logWarn(requestId, `Field name "${fieldName}" contains invalid characters`);
		return false;
	}
	
	logDebug(requestId, `Field definition validated: "${fieldName}" (${fieldType})`);
	return true;
}

/**
 * Interface for field type compatibility result
 */
export interface FieldTypeCompatibilityResult {
	/** Whether the types are directly compatible */
	isCompatible: boolean;
	/** Whether the types can be gracefully handled despite incompatibility */
	isGracefullyHandled: boolean;
	/** Level of compatibility (exact, compatible, graceful, incompatible) */
	compatibilityLevel: "exact" | "compatible" | "graceful" | "incompatible";
	/** Descriptive message about the compatibility */
	message: string;
	/** Whether data transformation may be required */
	requiresTransformation: boolean;
}

/**
 * Comprehensive field type compatibility checking
 *
 * Provides detailed compatibility analysis between source and target field types
 * with graceful handling of common mismatches like PHONE/telephone and TEXT/textarea.
 *
 * @param sourceType - Source field type
 * @param targetType - Target field type
 * @param fieldName - Field name for logging
 * @param direction - Conversion direction for logging
 * @param requestId - Request ID for logging
 * @returns Detailed compatibility result
 */
export function checkFieldTypeCompatibility(
	sourceType: string,
	targetType: string,
	fieldName: string,
	direction: string,
	requestId: string,
): FieldTypeCompatibilityResult {
	const normalizedSource = sourceType.toLowerCase().trim();
	const normalizedTarget = targetType.toLowerCase().trim();

	// Exact match
	if (normalizedSource === normalizedTarget) {
		return {
			isCompatible: true,
			isGracefullyHandled: true,
			compatibilityLevel: "exact",
			message: "Exact type match",
			requiresTransformation: false,
		};
	}

	// Check compatibility matrix
	if (areTypesCompatible(normalizedSource, normalizedTarget)) {
		return {
			isCompatible: true,
			isGracefullyHandled: true,
			compatibilityLevel: "compatible",
			message: "Types are compatible via compatibility matrix",
			requiresTransformation: true,
		};
	}

	// Special handling for common compatible mismatches
	const compatibleMismatches = getCompatibleMismatches();
	const mismatch = compatibleMismatches.find(
		(m) =>
			(normalizedSource === m.source && normalizedTarget === m.target) ||
			(normalizedSource === m.target && normalizedTarget === m.source)
	);

	if (mismatch) {
		logDebug(
			requestId,
			`${direction} compatible type mismatch handled: ${sourceType} ↔ ${targetType} for field "${fieldName}"`
		);
		return {
			isCompatible: false,
			isGracefullyHandled: true,
			compatibilityLevel: "graceful",
			message: `Compatible mismatch: ${mismatch.description}`,
			requiresTransformation: true,
		};
	}

	// Check if types can be gracefully converted to text
	if (canConvertToText(normalizedSource) && canConvertToText(normalizedTarget)) {
		logDebug(
			requestId,
			`${direction} graceful text conversion: ${sourceType} → ${targetType} for field "${fieldName}"`
		);
		return {
			isCompatible: false,
			isGracefullyHandled: true,
			compatibilityLevel: "graceful",
			message: "Types can be gracefully converted via text representation",
			requiresTransformation: true,
		};
	}

	// Incompatible types
	logWarn(
		requestId,
		`${direction} incompatible types: ${sourceType} → ${targetType} for field "${fieldName}". Sync may fail or produce unexpected results.`
	);
	return {
		isCompatible: false,
		isGracefullyHandled: false,
		compatibilityLevel: "incompatible",
		message: "Types are incompatible and cannot be gracefully converted",
		requiresTransformation: false,
	};
}

/**
 * Get list of compatible type mismatches that should be handled gracefully
 */
function getCompatibleMismatches(): Array<{
	source: string;
	target: string;
	description: string;
}> {
	return [
		{
			source: "phone",
			target: "telephone",
			description: "Phone and telephone fields are functionally equivalent",
		},
		{
			source: "text",
			target: "textarea",
			description: "Text and textarea fields can be converted with formatting adjustments",
		},
		{
			source: "email",
			target: "text",
			description: "Email fields can be stored as text with validation",
		},
		{
			source: "number",
			target: "text",
			description: "Number fields can be stored as text with parsing",
		},
		{
			source: "boolean",
			target: "radio",
			description: "Boolean and radio fields can be converted with Yes/No options",
		},
		{
			source: "select",
			target: "radio",
			description: "Select and radio fields are functionally similar",
		},
		{
			source: "date",
			target: "text",
			description: "Date fields can be stored as text with formatting",
		},
		{
			source: "url",
			target: "text",
			description: "URL fields can be stored as text",
		},
	];
}

/**
 * Check if a field type can be converted to text representation
 */
function canConvertToText(fieldType: string): boolean {
	const textConvertibleTypes = [
		"text", "textarea", "string", "varchar", "char",
		"email", "phone", "telephone", "url", "website",
		"number", "decimal", "float", "integer",
		"date", "datetime", "time", "timestamp",
		"boolean", "select", "radio"
	];

	return textConvertibleTypes.includes(fieldType.toLowerCase());
}
