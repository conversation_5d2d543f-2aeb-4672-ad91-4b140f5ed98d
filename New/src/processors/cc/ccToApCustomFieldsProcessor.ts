/**
 * CC to AP Custom Fields Processor
 *
 * Handles synchronization of custom fields from CliniCore (CC) to AutoPatient (AP) by:
 * 1. Fetching CC patient custom field data using the custom field IDs
 * 2. Filtering out standard contact fields to prevent conflicts
 * 3. Mapping CC custom field values to AP custom field format with proper Unicode handling
 * 4. Creating AP custom fields with appropriate data types if they don't exist
 * 5. Updating the AP contact with the synchronized custom field values
 *
 * Features:
 * - Unicode-aware field name matching for international characters
 * - Data type mapping between CC and AP custom field types
 * - Prevention of standard contact field conversion to custom fields
 * - Proper handling of multiple values in CC custom fields
 */

import {
	createApCustomField,
	transformBooleanValue,
} from "@ccProcessor/customFieldCreator";
import { findExistingApFieldWithApiCheck } from "@ccProcessor/fieldMatcher";
import type {
	APGetCustomFieldType,
	GetCCCustomField,
	GetCCPatientCustomField,
	GetCCPatientType,
	PostAPContactType,
} from "@type";
import { apCustomfield, contactReq, patientReq } from "@/apiClient";
import { logApiError } from "@/utils/errorLogger";
import { logDebug, logError, logInfo, logProcessingStep } from "@/utils/logger";

/**
 * Standard contact fields that should not be converted to custom fields
 * Based on PostAPContactType interface to prevent conflicts with core contact data
 *
 * ENHANCED: Comprehensive list including all AP standard fields and international variations
 */
const STANDARD_CONTACT_FIELDS = [
	// Core AP standard fields
	"email",
	"phone",
	"name",
	"firstName",
	"lastName",
	"timezone",
	"dnd",
	"source",
	"assignedTo",
	"address1",
	"city",
	"state",
	"country",
	"postalCode",
	"tags",
	"dateOfBirth",
	"ssn",
	"gender",
	"customFields",
	"companyName",
	"website",
	"type",
	"attachments",

	// Common English variations
	"first name",
	"last name",
	"date of birth",
	"phone number",
	"email address",
	"postal code",
	"zip code",
	"zip",
	"zipcode",
	"address",
	"street address",
	"company name",
	"company",
	"organization",
	"employer",
	"web site",
	"homepage",
	"url",
	"social security number",
	"social security",
	"tax id",
	"national id",
	"full name",
	"contact type",
	"patient type",

	// German variations
	"vorname",
	"nachname",
	"geburtsdatum",
	"telefon",
	"e-mail",
	"adresse",
	"postleitzahl",
	"firma",
	"unternehmen",
	"webseite",
	"geschlecht",
	"bundesland",
	"land",
	"sozialversicherungsnummer",

	// French variations
	"prenom",
	"nom",
	"nom de famille",
	"date de naissance",
	"telephone",
	"courriel",
	"courrier electronique",
	"code postal",
	"ville",
	"etat",
	"province",
	"pays",
	"entreprise",
	"site web",
	"sexe",
	"numero d'assurance sociale",

	// Italian variations
	"nome",
	"cognome",
	"data di nascita",
	"telefono",
	"posta elettronica",
	"indirizzo",
	"codice postale",
	"citta",
	"provincia",
	"regione",
	"paese",
	"azienda",
	"sito web",
	"sesso",
	"codice fiscale",

	// Spanish variations
	"nombre",
	"apellido",
	"fecha de nacimiento",
	"telefono",
	"correo electronico",
	"direccion",
	"codigo postal",
	"ciudad",
	"estado",
	"pais",
	"empresa",
	"sitio web",
	"sexo",
	"numero de seguridad social",

	// Portuguese variations
	"nome",
	"sobrenome",
	"data de nascimento",
	"telefone",
	"email",
	"endereco",
	"codigo postal",
	"cidade",
	"estado",
	"pais",
	"empresa",
	"site",
	"sexo",
	"numero de seguranca social",

	// Additional common variations
	"mobile",
	"mobile phone",
	"cell phone",
	"cellular",
	"handy",
	"mobiltelefon",
	"cellphone",
	"street",
	"street1",
	"address line 1",
	"birth date",
	"birthdate",
	"dob",
	"surname",
	"family name",
	"given name",
	"middle name",
	"title",
	"prefix",
	"suffix",
];

/**
 * Mapping configuration for CC custom fields to AP standard contact fields
 * Maps CC custom field names/labels to AP standard field names
 *
 * ENHANCED: Comprehensive mapping for all AP standard fields with international variations
 */
const CC_TO_AP_STANDARD_FIELD_MAPPING: Record<string, string> = {
	// Phone field variations
	"phone-mobile": "phone",
	phonemobile: "phone",
	"phone mobile": "phone",
	"telefon mobil": "phone",
	"telefon-mobil": "phone",
	telefon: "phone",
	telephone: "phone",
	mobile: "phone",
	handy: "phone",
	mobiltelefon: "phone",
	"cell phone": "phone",
	"cell-phone": "phone",
	cellular: "phone",
	"mobile phone": "phone",
	"mobile number": "phone",
	"mobile-number": "phone",
	cell: "phone",
	cellphone: "phone",
	handynummer: "phone",
	mobilnummer: "phone",
	"phone number": "phone",
	"numero de telefono": "phone",
	"numero de telephone": "phone",
	"numero di telefono": "phone",
	"numero de telefone": "phone",

	// Email field variations
	"e-mail": "email",
	"email address": "email",
	"e-mail address": "email",
	"e-mail-adresse": "email",
	"email-adresse": "email",
	"electronic mail": "email",
	courriel: "email",
	"courrier electronique": "email",
	"posta elettronica": "email",
	"correo electronico": "email",
	"correio eletronico": "email",

	// Name field variations
	"first name": "firstName",
	"first-name": "firstName",
	vorname: "firstName",
	prenom: "firstName",
	nome: "firstName", // Italian/Portuguese first name
	"given name": "firstName",
	"last name": "lastName",
	"last-name": "lastName",
	nachname: "lastName",
	"nom de famille": "lastName",
	cognome: "lastName", // Italian
	apellido: "lastName", // Spanish
	sobrenome: "lastName", // Portuguese
	surname: "lastName",
	"family name": "lastName",
	"full name": "name",
	"complete name": "name",
	"patient name": "name",
	"contact name": "name",

	// Date of birth variations
	"date of birth": "dateOfBirth",
	"date-of-birth": "dateOfBirth",
	dob: "dateOfBirth",
	"birth date": "dateOfBirth",
	birthdate: "dateOfBirth",
	geburtsdatum: "dateOfBirth", // German
	"date de naissance": "dateOfBirth", // French
	"data di nascita": "dateOfBirth", // Italian
	"fecha de nacimiento": "dateOfBirth", // Spanish
	"data de nascimento": "dateOfBirth", // Portuguese

	// Gender variations
	gender: "gender",
	sex: "gender",
	geschlecht: "gender", // German
	sexe: "gender", // French
	sesso: "gender", // Italian
	sexo: "gender", // Spanish/Portuguese

	// Address field variations
	address: "address1",
	"street address": "address1",
	street: "address1",
	street1: "address1",
	"address line 1": "address1",
	"address 1": "address1",
	adresse: "address1", // German/French
	indirizzo: "address1", // Italian
	direccion: "address1", // Spanish
	endereco: "address1", // Portuguese

	// City variations
	city: "city",
	stadt: "city", // German
	ville: "city", // French
	citta: "city", // Italian
	ciudad: "city", // Spanish
	cidade: "city", // Portuguese

	// State/Province variations
	state: "state",
	province: "state",
	region: "state",
	bundesland: "state", // German
	provincia: "state", // Italian/Spanish
	estado: "state", // Spanish/Portuguese
	etat: "state", // French

	// Country variations
	country: "country",
	land: "country", // German
	pays: "country", // French
	paese: "country", // Italian
	pais: "country", // Spanish/Portuguese

	// Postal code variations
	"postal code": "postalCode",
	"postal-code": "postalCode",
	zip: "postalCode",
	"zip code": "postalCode",
	"zip-code": "postalCode",
	zipcode: "postalCode",
	postleitzahl: "postalCode", // German
	"code postal": "postalCode", // French
	"codice postale": "postalCode", // Italian
	"codigo postal": "postalCode", // Spanish/Portuguese

	// SSN variations
	ssn: "ssn",
	"social security number": "ssn",
	"social security": "ssn",
	"social-security-number": "ssn",
	"social-security": "ssn",
	"tax id": "ssn",
	"tax-id": "ssn",
	"national id": "ssn",
	"national-id": "ssn",
	sozialversicherungsnummer: "ssn", // German
	"numero d'assurance sociale": "ssn", // French
	"codice fiscale": "ssn", // Italian
	"numero de seguridad social": "ssn", // Spanish
	"numero de seguranca social": "ssn", // Portuguese

	// Company variations
	"company name": "companyName",
	"company-name": "companyName",
	company: "companyName",
	organization: "companyName",
	employer: "companyName",
	firma: "companyName", // German
	unternehmen: "companyName", // German
	entreprise: "companyName", // French
	azienda: "companyName", // Italian
	empresa: "companyName", // Spanish/Portuguese

	// Website variations
	website: "website",
	"web site": "website",
	"web-site": "website",
	homepage: "website",
	url: "website",
	webseite: "website", // German
	"site web": "website", // French
	"sito web": "website", // Italian
	"sitio web": "website", // Spanish
	site: "website", // Portuguese

	// Timezone variations
	timezone: "timezone",
	"time zone": "timezone",
	"time-zone": "timezone",
	tz: "timezone",
	zeitzone: "timezone", // German
	"fuseau horaire": "timezone", // French
	"fuso orario": "timezone", // Italian
	"zona horaria": "timezone", // Spanish
	"fuso horario": "timezone", // Portuguese
};

/**
 * Interface for custom field mapping result
 */
interface CustomFieldMapping {
	/** AP custom field ID */
	id: string;
	/** Field value to set */
	field_value: string;
}

/**
 * Check if a field name represents a standard contact field
 * Uses exact matching following v3Integration pattern
 */
function isStandardContactField(fieldName: string): boolean {
	return STANDARD_CONTACT_FIELDS.some(
		(standardField) =>
			standardField === fieldName ||
			standardField.toLowerCase() === fieldName.toLowerCase(),
	);
}

/**
 * Check if a CC custom field should be mapped to an AP standard field
 * Returns the AP standard field name if mapping exists, null otherwise
 */
function getStandardFieldMapping(
	ccFieldName: string,
	ccFieldLabel: string,
): string | null {
	// Check both field name and label against the mapping using exact matching
	for (const [ccFieldPattern, apStandardField] of Object.entries(
		CC_TO_AP_STANDARD_FIELD_MAPPING,
	)) {
		if (
			ccFieldPattern === ccFieldName ||
			ccFieldPattern === ccFieldLabel ||
			ccFieldPattern.toLowerCase() === ccFieldName.toLowerCase() ||
			ccFieldPattern.toLowerCase() === ccFieldLabel.toLowerCase()
		) {
			return apStandardField as string;
		}
	}

	return null;
}

/**
 * Extract standard field mappings from CC custom fields
 * Returns a map of AP standard field names to their values
 */
function extractStandardFieldMappings(
	ccPatientCustomFields: GetCCPatientCustomField[],
	requestId: string,
): Record<string, string> {
	const standardMappings: Record<string, string> = {};

	logInfo(
		requestId,
		`Extracting standard field mappings from ${ccPatientCustomFields.length} CC custom fields`,
	);

	for (const ccCustomField of ccPatientCustomFields) {
		const fieldName = ccCustomField.field.name;
		const fieldLabel = ccCustomField.field.label;

		// Check if this CC custom field should map to an AP standard field
		const apStandardField = getStandardFieldMapping(fieldName, fieldLabel);

		if (apStandardField) {
			// Extract field value
			const fieldValue = extractFieldValues(ccCustomField);

			if (fieldValue && fieldValue.trim() !== "") {
				// Handle multiple mappings to the same standard field - prioritize non-empty values
				if (
					!standardMappings[apStandardField] ||
					standardMappings[apStandardField].trim() === ""
				) {
					standardMappings[apStandardField] = fieldValue.trim();
					logDebug(
						requestId,
						`Standard field mapping: "${fieldName}" (${fieldLabel}) -> AP.${apStandardField} = "${fieldValue.substring(
							0,
							50,
						)}${fieldValue.length > 50 ? "..." : ""}"`,
					);
				} else {
					logDebug(
						requestId,
						`Skipping duplicate standard field mapping: "${fieldName}" (${fieldLabel}) -> AP.${apStandardField} (already mapped)`,
					);
				}
			} else {
				logDebug(
					requestId,
					`Skipping standard field mapping for empty value: ${fieldName} (${fieldLabel})`,
				);
			}
		}
	}

	logInfo(
		requestId,
		`Extracted ${Object.keys(standardMappings).length} standard field mappings`,
	);
	return standardMappings;
}

/**
 * Extract and combine multiple values from CC custom field
 * Handles different separation strategies based on field type
 */
function extractFieldValues(ccCustomField: GetCCPatientCustomField): string {
	if (!ccCustomField.values || ccCustomField.values.length === 0) {
		return "";
	}

	const values = ccCustomField.values
		.map((v) => v.value)
		.filter((v): v is string => v != null && v.trim() !== "");

	if (values.length === 0) {
		return "";
	}

	// For single value, return as-is
	if (values.length === 1) {
		return values[0];
	}

	// For multiple values, choose separation strategy based on field type
	const fieldType = ccCustomField.field.type?.toLowerCase() || "";

	if (fieldType.includes("multiselect") || fieldType.includes("checkbox")) {
		// Use comma separation for multi-select fields
		return values.join(", ");
	} else if (fieldType.includes("textarea") || fieldType.includes("text")) {
		// Use newline separation for text areas
		return values.join("\n");
	} else {
		// Default to comma separation
		return values.join(", ");
	}
}

/**
 * Synchronize custom fields from CC patient to AP contact
 *
 * @param requestId - Request ID from Hono context for logging correlation
 * @param localPatientId - Local database patient ID for logging context
 * @param ccPatientData - CC patient data containing custom field IDs
 * @param apContactId - AP contact ID to update with custom fields
 * @returns Promise<void> - Completes sync or throws error
 */
export async function syncCcToApCustomFields(
	requestId: string,
	localPatientId: string,
	ccPatientData: GetCCPatientType,
	apContactId: string,
): Promise<void> {
	logProcessingStep(
		requestId,
		`Starting custom field sync for CC Patient ${ccPatientData.id} -> AP Contact ${apContactId} (Local Patient ID: ${localPatientId})`,
	);

	// Step 1: Check if patient has custom fields
	if (!ccPatientData.customFields || ccPatientData.customFields.length === 0) {
		logInfo(
			requestId,
			`No custom fields found for CC patient ${ccPatientData.id}`,
		);
		return;
	}

	try {
		// Step 2: Fetch CC patient custom field data
		logInfo(
			requestId,
			`Fetching ${ccPatientData.customFields.length} custom fields from CC`,
		);
		const ccPatientCustomFields = await patientReq.customFields(
			ccPatientData.customFields,
		);

		if (!ccPatientCustomFields || ccPatientCustomFields.length === 0) {
			logInfo(requestId, `No custom field data returned from CC`);
			return;
		}

		// Step 3: Extract and apply standard field mappings
		const standardFieldMappings = extractStandardFieldMappings(
			ccPatientCustomFields,
			requestId,
		);

		// Build update payload for standard fields
		const standardFieldUpdate: Partial<PostAPContactType> = {};

		// Apply standard field mappings to AP contact if any exist
		if (Object.keys(standardFieldMappings).length > 0) {
			logInfo(
				requestId,
				`Applying ${
					Object.keys(standardFieldMappings).length
				} standard field mappings to AP contact`,
			);

			for (const [fieldName, value] of Object.entries(standardFieldMappings)) {
				if (fieldName === "phone") {
					standardFieldUpdate.phone = value;
				} else if (fieldName === "email") {
					standardFieldUpdate.email = value;
				}
				// Add more standard field mappings as needed
			}
		}

		// Step 4: Filter out excluded fields and extract valid custom field mappings
		const validCustomFields = await filterAndMapCustomFields(
			ccPatientCustomFields,
			requestId,
		);

		if (validCustomFields.length === 0) {
			logInfo(requestId, `No valid custom fields to sync after filtering`);
			return;
		}

		// Step 4: Get all AP custom fields for mapping (with validation)
		logInfo(requestId, `Fetching AP custom fields for mapping`);
		const apCustomFields = await apCustomfield.all();

		// AutoPatient API guarantees unique fieldKeys - no validation needed
		logDebug(
			requestId,
			`Retrieved ${apCustomFields.length} AP custom fields for mapping`,
		);

		// Step 5: Map CC fields to AP format and create missing fields
		const apCustomFieldMappings = await mapToApCustomFields(
			validCustomFields,
			apCustomFields,
			requestId,
		);

		if (apCustomFieldMappings.length === 0) {
			logInfo(requestId, `No custom field mappings created`);
			return;
		}

		// Step 6: Update AP contact with custom fields
		logInfo(
			requestId,
			`Upserting AP contact with ${apCustomFieldMappings.length} custom fields`,
		);
		let upsertPayload: Partial<PostAPContactType> = {
			customFields: apCustomFieldMappings,
		};
		if (
			Object.keys(standardFieldMappings).length > 0 &&
			Object.keys(standardFieldUpdate).length > 0
		) {
			upsertPayload = {
				...upsertPayload,
				...standardFieldUpdate,
			};
		}

		// CRITICAL: Remove tags field to avoid conflicts in upsert operations (as per API docs)
		delete upsertPayload.tags;

		// Use upsert instead of update as per API documentation
		await contactReq.upsert(upsertPayload);

		logInfo(requestId, `Custom field sync completed successfully`);
	} catch (error) {
		logError(requestId, `Custom field sync failed:`, error);

		// Log the error but don't throw to avoid blocking main patient processing
		await logApiError(
			error as Error,
			requestId,
			"custom_field_sync",
			"cc_to_ap_sync",
			{
				ccPatientId: ccPatientData.id,
				apContactId,
				customFieldCount: ccPatientData.customFields?.length || 0,
			},
		);

		// Re-throw to let caller decide how to handle
		throw error;
	}
}

/**
 * Filter CC custom fields and extract valid field mappings
 * Excludes standard contact fields and empty values
 *
 * @param ccPatientCustomFields - CC patient custom field data
 * @param requestId - Request ID for logging
 * @returns Promise<Array> - Valid custom field data
 */
async function filterAndMapCustomFields(
	ccPatientCustomFields: GetCCPatientCustomField[],
	requestId: string,
): Promise<Array<{ field: GetCCCustomField; value: string }>> {
	const validFields: Array<{ field: GetCCCustomField; value: string }> = [];

	for (const ccCustomField of ccPatientCustomFields) {
		const fieldName = ccCustomField.field.name;
		const fieldLabel = ccCustomField.field.label;

		// Check if field is a standard contact field that should not be converted to custom field
		if (
			isStandardContactField(fieldName) ||
			isStandardContactField(fieldLabel)
		) {
			logDebug(
				requestId,
				`Excluding standard contact field: ${fieldName} (${fieldLabel})`,
			);
			continue;
		}

		// Check if field should be mapped to an AP standard field instead of custom field
		const standardFieldMapping = getStandardFieldMapping(fieldName, fieldLabel);
		if (standardFieldMapping) {
			logDebug(
				requestId,
				`Excluding field mapped to standard field: ${fieldName} (${fieldLabel}) -> AP.${standardFieldMapping}`,
			);
			continue;
		}

		// Extract field value using improved multiple values handling
		const fieldValue = extractFieldValues(ccCustomField);

		if (!fieldValue || fieldValue.trim() === "") {
			logDebug(requestId, `Skipping field with empty value: ${fieldName}`);
			continue;
		}

		validFields.push({
			field: ccCustomField.field,
			value: fieldValue,
		});

		logDebug(
			requestId,
			`Valid field: ${fieldName} (${fieldLabel}) = ${fieldValue.substring(
				0,
				100,
			)}${fieldValue.length > 100 ? "..." : ""}`,
		);
	}

	return validFields;
}

/**
 * Map CC custom fields to AP custom field format
 * Creates missing AP custom fields with proper data types as needed
 *
 * @param validCustomFields - Filtered CC custom field data
 * @param apCustomFields - Existing AP custom fields
 * @param requestId - Request ID for logging
 * @returns Promise<CustomFieldMapping[]> - AP custom field mappings
 */
async function mapToApCustomFields(
	validCustomFields: Array<{ field: GetCCCustomField; value: string }>,
	apCustomFields: APGetCustomFieldType[],
	requestId: string,
): Promise<CustomFieldMapping[]> {
	const mappings: CustomFieldMapping[] = [];
	let createdCount = 0;
	let existingCount = 0;

	logInfo(
		requestId,
		`Starting custom field mapping for ${validCustomFields.length} CC fields against ${apCustomFields.length} existing AP fields`,
	);

	for (const { field, value } of validCustomFields) {
		try {
			// Try to find existing AP custom field using enhanced matching with API fallback
			let apCustomField = await findExistingApFieldWithApiCheck(
				apCustomFields,
				field,
				requestId,
			);

			// Create AP custom field if it doesn't exist
			if (!apCustomField) {
				try {
					apCustomField = await createApCustomField(
						field,
						value,
						apCustomFields,
						requestId,
					);
					createdCount++;
				} catch (error) {
					logError(
						requestId,
						`Failed to create AP custom field for "${field.name}":`,
						error,
					);
					// Continue with other fields rather than failing completely
					continue;
				}
			} else {
				logDebug(
					requestId,
					`Using existing AP custom field: "${apCustomField.name}" with ID: ${apCustomField.id}`,
				);
				existingCount++;
			}

			// Transform value for boolean fields
			const transformedValue = transformBooleanValue(value, field.type);

			mappings.push({
				id: apCustomField.id,
				field_value: transformedValue,
			});

			logDebug(
				requestId,
				`Mapped: "${field.label}" -> AP Field ID ${
					apCustomField.id
				} (${transformedValue.substring(0, 50)}${
					transformedValue.length > 50 ? "..." : ""
				})${transformedValue !== value ? ` [transformed from: ${value}]` : ""}`,
			);
		} catch (error) {
			logError(requestId, `Failed to map field "${field.name}":`, error);
			// Continue with other fields rather than failing completely
		}
	}

	logInfo(
		requestId,
		`Custom field mapping completed: ${mappings.length} total mappings (${createdCount} created, ${existingCount} existing)`,
	);

	return mappings;
}
